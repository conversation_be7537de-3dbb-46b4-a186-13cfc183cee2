import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_lock_provider.dart';
import '../widgets/pin_screen.dart';
import '../services/auth_service.dart';
import '../widgets/biometric_guidance_dialog.dart';

class AppLockScreen extends StatefulWidget {
  final Widget child;

  const AppLockScreen({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<AppLockScreen> createState() => _AppLockScreenState();
}

class _AppLockScreenState extends State<AppLockScreen> with WidgetsBindingObserver {
  bool _isBiometricAuthInProgress = false;
  
  // We don't need to track orientation changes here anymore
  // The AppLifecycleManager will handle that for us
  
  @override
  void initState() {
    super.initState();
    // We still observe lifecycle events for biometric checks
    WidgetsBinding.instance.addObserver(this);
    
    // CRITICAL FIX: Sync instance variable with the static flag on widget creation
    // This ensures the cancellation state persists across widget rebuilds
    _biometricAuthCanceled = _systemWideBiometricCanceled;
    
    print("DEBUG: AppLockScreen: Widget initialized, biometricCanceled: $_biometricAuthCanceled, systemWide: $_systemWideBiometricCanceled");
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
  
  // Static flag that persists across rebuilds to track biometric cancellation system-wide
  // This ensures even if the widget rebuilds, we won't trigger biometrics again after cancellation
  static bool _systemWideBiometricCanceled = false;
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Handle different lifecycle states
    if (state == AppLifecycleState.resumed) {
      // CRITICAL FIX: Only reset biometric cancellation when app is completely relaunched
      // For simple backgrounding/foregrounding, we remember the user's preference
      print("DEBUG: AppLockScreen: App resumed, biometricCanceled: $_biometricAuthCanceled, systemWide: $_systemWideBiometricCanceled");
      
      // Only check biometrics if they haven't been canceled
      if (!_systemWideBiometricCanceled && !_biometricAuthCanceled) {
        // The AppLifecycleManager will handle the decision of whether this is a true resume
        // We just need to check if biometrics should be triggered
        _initialBiometricCheck();
      } else {
        print("DEBUG: AppLockScreen: Skipping biometric check due to previous cancellation");
      }
    } else if (state == AppLifecycleState.paused) {
      // We do NOT reset the cancellation flag when app is paused
      // Instead, we remember the user's preference until the app is fully restarted
      print("DEBUG: AppLockScreen: App paused, preserving biometric preference: canceled=$_biometricAuthCanceled");
    }
  }

  // This is used for the initial check and app resume - no UI messages
  Future<void> _initialBiometricCheck() async {
    print("DEBUG: AppLockScreen: Starting initial biometric check");
    
    // IMPORTANT: Don't auto-trigger biometrics if the user has already canceled it
    // Check both the local and system-wide flags
    if (_biometricAuthCanceled || _systemWideBiometricCanceled || _alwaysUsePinForSession) {
      print("DEBUG: AppLockScreen: Skipping biometric check due to previous cancellation (local=$_biometricAuthCanceled, system=$_systemWideBiometricCanceled)");
      return;
    }
    
    // Make sure we have a safe provider reference
    _safeGetProvider();
    
    // Use our class-level provider reference when possible
    AppLockProvider provider;
    if (_appLockProvider != null) {
      provider = _appLockProvider!;
    } else {
      try {
        provider = Provider.of<AppLockProvider>(context, listen: false);
      } catch (e) {
        print("DEBUG: AppLockScreen: Error getting provider in _initialBiometricCheck: $e");
        return; // Exit if we can't get the provider
      }
    }
    
    // Add a small delay before performing biometric check
    // This gives time for the widget to fully build and stabilize
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Safety check to prevent calling setState on unmounted widget
    if (!mounted) {
      print("DEBUG: AppLockScreen: Widget not mounted, skipping biometric check");
      return;
    }
    
    // Initialize the provider if needed
    if (!provider.isInitialized) {
      print("DEBUG: AppLockScreen: Provider not initialized, initializing");
      await provider.initialize();
    }
    
    // Check if we need to authenticate and if biometrics are enabled
    final authRequired = await provider.isAuthenticationRequired();
    print("DEBUG: AppLockScreen: Authentication required: $authRequired");
    print("DEBUG: AppLockScreen: Biometrics enabled: ${provider.isBiometricEnabled}");
    
    if (!authRequired) {
      print("DEBUG: AppLockScreen: No authentication needed");
      return;
    }
    
    // Check enrollment status if biometrics are enabled
    if (provider.isBiometricEnabled) {
      final needsEnrollment = await provider.isBiometricEnrollmentNeeded();
      print("DEBUG: AppLockScreen: Biometric enrollment needed: $needsEnrollment");
      
      // Only proceed with biometrics if enrollment is not needed
      if (!needsEnrollment && !_biometricAuthCanceled) {
        // Check if widget is still mounted before proceeding
        if (!mounted) return;
        
        // Set a flag to show we're working on biometric auth, which lets our UI know
        // to show the PIN screen if it gets canceled
        setState(() {
          _isBiometricAuthInProgress = true;
        });
        
        // Try biometric authentication with a safe approach
        try {
          print("DEBUG: AppLockScreen: Attempting biometric authentication");
          final authenticated = await provider.authenticateWithBiometrics();
          
          if (authenticated) {
            print("DEBUG: AppLockScreen: Biometric authentication successful");
            // No need to navigate anywhere, provider will handle authentication state
          } else {
            print("DEBUG: AppLockScreen: Biometric authentication failed or canceled");
            // Set cancellation flag so the PIN screen appears and we don't retry biometrics
            _biometricAuthCanceled = true;
            _biometricCancelCount++;
          }
        } catch (e) {
          print("DEBUG: AppLockScreen: Biometric authentication error: $e");
          // Mark as canceled on error too
          _biometricAuthCanceled = true;
        } finally {
          // Always ensure we clean up the biometric auth flag
          if (mounted) {
            setState(() {
              _isBiometricAuthInProgress = false;
            });
          }
        }
      }
    }
    
    // Force a rebuild to show PIN screen instead of automatic biometric auth
    if (mounted) {
      setState(() {
        // Just trigger a rebuild to show PIN entry screen
      });
    }
  }
  
  // Track if user has explicitly canceled biometric auth to prevent auto-retry loops
  bool _biometricAuthCanceled = false;
  
  // Track how many times biometric auth has been canceled in this session
  int _biometricCancelCount = 0;
  
  // Track if user has chosen to always use PIN for this session
  bool _alwaysUsePinForSession = false;

  // Store provider reference to avoid context lookups when widget might be deactivated
  AppLockProvider? _appLockProvider;
  
  // Get the provider safely without relying on context in unsafe situations
  void _safeGetProvider() {
    try {
      if (mounted) {
        _appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
      }
    } catch (e) {
      print("DEBUG: AppLockScreen: Error getting provider: $e");
    }
  }
  
  // This is called from the explicit fingerprint button - shows UI feedback
  Future<void> _authenticateWithBiometrics() async {
    print("DEBUG: AppLockScreen: Manual biometric authentication requested");
    
    // Don't attempt biometric auth if the user has chosen to always use PIN for this session
    if (_alwaysUsePinForSession) {
      print("DEBUG: AppLockScreen: Skipping biometric auth because user chose to always use PIN");
      return;
    }
    
    // Don't attempt biometric auth if the user previously canceled it this time
    if (_biometricAuthCanceled) {
      print("DEBUG: AppLockScreen: Skipping biometric auth because user previously canceled");
      return;
    }
    
    // Make sure we have a safe provider reference
    _safeGetProvider();
    
    // Use our class-level provider reference when possible
    AppLockProvider provider;
    if (_appLockProvider != null) {
      provider = _appLockProvider!;
    } else {
      try {
        provider = Provider.of<AppLockProvider>(context, listen: false);
      } catch (e) {
        print("DEBUG: AppLockScreen: Error getting provider in _authenticateWithBiometrics: $e");
        return; // Exit if we can't get the provider
      }
    }
    
    setState(() {
      _isBiometricAuthInProgress = true;
    });
    
    try {
      print("DEBUG: AppLockScreen: Attempting biometric authentication");
      
      // Try authentication
      final success = await provider.authenticateWithBiometrics();
      print("DEBUG: AppLockScreen: Biometric authentication result: $success");
      
      if (success) {
        // CRITICAL FIX: Double-check authentication state
        print("DEBUG: AppLockScreen: Authentication reported success");
        
        // Verify that the state was actually updated
        print("DEBUG: AppLockScreen: Checking isAuthenticated = ${provider.isAuthenticated}");
        
        // If authentication state wasn't updated for some reason, force it
        if (!provider.isAuthenticated) {
          print("DEBUG: AppLockScreen: State inconsistency detected! Forcing authentication");
          provider.forceAuthentication();
        }
        
        // Reset the canceled flag on success
        _biometricAuthCanceled = false;
        
        // Force rebuild
        if (mounted) {
          print("DEBUG: AppLockScreen: Triggering UI rebuild after authentication");
          setState(() {});
        }
      } else {
        // The user canceled or authentication failed - quietly fall back to PIN
        print("DEBUG: AppLockScreen: Authentication canceled or failed - falling back to PIN");
        
        // CRITICAL FIX: Mark biometric auth as canceled both locally and system-wide
        _biometricAuthCanceled = true;
        _systemWideBiometricCanceled = true; // This persists across widget rebuilds
        
        // Increment cancel counter
        _biometricCancelCount++;
        print("DEBUG: AppLockScreen: Biometric CANCELED! Cancel count: $_biometricCancelCount, systemWide: $_systemWideBiometricCanceled");
        
        if (mounted) {
          setState(() {}); // Update UI to clear any pending states
          
          // If user has canceled biometrics multiple times, offer to remember this preference for the session
          if (_biometricCancelCount >= 2 && !_alwaysUsePinForSession) {
            _showPinPreferenceOption();
          }
        }
      }
    } catch (e) {
      print("DEBUG: AppLockScreen: Error in biometric authentication: $e");
      
      // On error, treat as canceled to prevent retry loops system-wide
      _biometricAuthCanceled = true;
      _systemWideBiometricCanceled = true; // This persists across widget rebuilds
      print("DEBUG: AppLockScreen: Biometric ERROR! Setting system-wide cancellation flag");
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Biometric authentication not available'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isBiometricAuthInProgress = false;
        });
        
        // Final check of authentication state - use our safe reference if possible
        try {
          AppLockProvider finalProvider = _appLockProvider ?? Provider.of<AppLockProvider>(context, listen: false);
          print("DEBUG: AppLockScreen: Final auth check, isAuthenticated = ${finalProvider.isAuthenticated}");
        } catch (e) {
          print("DEBUG: AppLockScreen: Error in final auth check: $e");
        }
        
        // One last rebuild to ensure UI reflects state
        setState(() {});
      }
    }
  }

  void _handlePinEntered(String pin) async {
    try {
      // Make sure we have a safe provider reference
      _safeGetProvider();
      
      // Use our class-level provider reference when possible
      AppLockProvider provider;
      if (_appLockProvider != null) {
        provider = _appLockProvider!;
      } else {
        try {
          provider = Provider.of<AppLockProvider>(context, listen: false);
        } catch (e) {
          print("DEBUG: AppLockScreen: Error getting provider in _handlePinEntered: $e");
          return; // Exit if we can't get the provider
        }
      }
      
      print("DEBUG: AppLockScreen: PIN entered, authenticating...");
      
      // Attempt authentication
      final result = await provider.authenticateWithPin(pin);
      print("DEBUG: AppLockScreen: PIN authentication result: $result");
      
      if (result) {
        // Successful authentication - make sure UI updates
        if (mounted) {
          print("DEBUG: AppLockScreen: PIN authenticated successfully, forcing state update");
          // Force update to ensure app shows main content
          setState(() {});
          
          // Double-check authentication state after a short delay
          Future.delayed(Duration(milliseconds: 100), () {
            if (mounted) {
              print("DEBUG: AppLockScreen: Post-delay auth check: ${provider.isAuthenticated}");
              if (!provider.isAuthenticated) {
                // Something went wrong, force authentication directly
                print("DEBUG: AppLockScreen: Authentication state inconsistency detected, forcing authentication");
                provider.forceAuthentication();
                setState(() {});
              }
            }
          });
        }
      } else if (!result && mounted) {
        // Show error for incorrect PIN
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Incorrect PIN. Please try again.'),
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(bottom: 16.0, left: 16.0, right: 16.0),
          ),
        );
      }
    } catch (e) {
      print("DEBUG: AppLockScreen: Error in PIN authentication: $e");
    }
  }

  // Removed unused _getSafeProvider method
  
  // Special handler for when user explicitly presses the biometric button
  // This allows users to re-enable biometrics even after they've previously cancelled it
  Future<void> _handleExplicitBiometricButtonPress() async {
    print("DEBUG: AppLockScreen: User explicitly pressed biometric button - overriding cancellation flags");
    
    // Clear all cancellation flags since user explicitly wants to use biometrics now
    _biometricAuthCanceled = false;
    _systemWideBiometricCanceled = false;
    _alwaysUsePinForSession = false;
    
    // Reset counter too
    _biometricCancelCount = 0;
    
    // Now proceed with biometric authentication as usual
    await _authenticateWithBiometrics();
  }
  
  // Show a subtle option to use PIN for the entire session after multiple cancellations
  // This aligns with the user's preference for a simplified security model
  void _showPinPreferenceOption() {
    // Only show if mounted and not already set
    if (!mounted || _alwaysUsePinForSession) return;
    
    // Show a subtle snackbar with an action rather than a full dialog
    // This maintains the minimalist design preference
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Would you prefer to use PIN for this session?'),
        duration: const Duration(seconds: 5),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.only(bottom: 16.0, left: 16.0, right: 16.0),
        action: SnackBarAction(
          label: 'Yes',
          onPressed: () {
            // Remember preference for this session only (not permanently)
            setState(() {
              _alwaysUsePinForSession = true;
              print("DEBUG: AppLockScreen: User chose to always use PIN for this session");
            });
          },
        ),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    print("DEBUG: AppLockScreen: Building AppLockScreen");
    
    // Safety check - if we can't access the provider, return a basic loading screen
    if (!mounted) {
      print("DEBUG: AppLockScreen: Widget not mounted during build, returning empty container");
      return Container(color: Colors.black);
    }
    
    // Use Consumer to ensure we always get the latest app lock state
    return Consumer<AppLockProvider>(
      builder: (context, appLockProvider, _) {
        print("DEBUG: AppLockScreen: Consumer rebuilding, auth=${appLockProvider.isAuthenticated}, lock=${appLockProvider.isLockEnabled}");
        
        // No lock needed or already authenticated - show the main app content
        if (!appLockProvider.isLockEnabled || appLockProvider.isAuthenticated) {
          print("DEBUG: AppLockScreen: ✓ Showing main app content");
          return widget.child;
        }
        
        // IMPORTANT: At this point, we know lock is enabled and user is NOT authenticated
        // This means we MUST show the lock screen and NOT the app content
        
        // CRITICAL FIX: Only auto-trigger biometrics when we have a proper PIN UI already visible
        // This ensures users can access PIN entry if they cancel biometrics
        bool hasPinScreenVisible = false;
        
        // We'll set a flag to track when PIN UI is fully visible before attempting biometrics
        WidgetsBinding.instance.addPostFrameCallback((_) {
          hasPinScreenVisible = true;
          
          // Now that PIN UI is guaranteed to be visible, we can safely try biometrics
          // The user will be able to cancel and still see the PIN screen
          if (mounted && 
              hasPinScreenVisible &&
              appLockProvider.isBiometricEnabled && 
              !_isBiometricAuthInProgress && 
              !appLockProvider.isAuthenticated && 
              !_biometricAuthCanceled && 
              !_systemWideBiometricCanceled && 
              !_alwaysUsePinForSession) { // Multiple checks to absolutely prevent retries
            // Wait just a moment longer to ensure the PIN UI is fully rendered
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted && !_biometricAuthCanceled && !_systemWideBiometricCanceled && !_alwaysUsePinForSession) {
                print("DEBUG: AppLockScreen: Auto-triggering biometric auth AFTER ensuring PIN UI is visible");
                _authenticateWithBiometrics();
              } else {
                print("DEBUG: AppLockScreen: Not triggering biometrics due to cancellation flags");
              }
            });
          }
        });
        
        // REMOVED: The loading indicator UI during biometric authentication
        // Now we'll always show the PIN UI underneath the biometric prompt, like in archive lock
        
        // Check if we need to show biometric enrollment guidance
        return FutureBuilder<bool>(
          future: appLockProvider.isBiometricEnrollmentNeeded(),
          initialData: false,
          builder: (context, enrollmentSnapshot) {
            final needsEnrollment = enrollmentSnapshot.data ?? false;
            print("DEBUG: AppLockScreen: Biometric enrollment needed: $needsEnrollment");
            
            // Show the PIN screen with appropriate options
            return Scaffold(
              body: OrientationBuilder(
                builder: (context, orientation) {
                  return Stack(
                    children: [
                      // The PIN screen is always shown when not authenticated
                      PinScreen(
                        title: 'Unlock Dark Notes',
                        subtitle: 'Enter your PIN to access your notes',
                        pinLength: 4,
                        onPinSubmitted: _handlePinEntered,
                        showBiometrics: appLockProvider.isBiometricEnabled && !needsEnrollment && !_isBiometricAuthInProgress,
                        onBiometricPressed: appLockProvider.isBiometricEnabled && !needsEnrollment && !_isBiometricAuthInProgress
                            ? _handleExplicitBiometricButtonPress // Use special handler for button press
                            : null,
                        hideVisibilityToggle: true, // Remove the eye button
                        isLandscape: orientation == Orientation.landscape,
                      ),
                      
                      // Show enrollment guidance dialog if needed
                      if (needsEnrollment && appLockProvider.isBiometricEnabled)
                        Positioned.fill(
                          child: Container(
                            color: Colors.black54,
                            child: Center(
                              child: BiometricGuidanceDialog(
                                onDismiss: () async {
                                  final newStatus = await appLockProvider.authService.getBiometricStatus();
                                  if (newStatus == BiometricStatus.enrolled) {
                                    await appLockProvider.silentlyUpdateBiometricEnrollment();
                                    if (mounted) setState(() {});
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                    ],
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
