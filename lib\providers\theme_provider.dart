import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider with ChangeNotifier {
  static const String _themePreferenceKey = 'theme_mode';
  bool _isDarkMode = true; // Use boolean for instant switching

  // Keep ThemeMode for MaterialApp compatibility
  ThemeMode get themeMode => _isDarkMode ? ThemeMode.dark : ThemeMode.light;
  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadThemePreference();
  }

  // Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isDark = prefs.getBool(_themePreferenceKey) ?? true; // Default to dark

      if (_isDarkMode != isDark) {
        _isDarkMode = isDark;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading theme preference: $e');
    }
  }

  // INSTANT theme toggle - no async operations
  void toggleTheme() {
    _isDarkMode = !_isDarkMode;

    // Notify ALL listeners immediately - this is the key to instant updates
    notifyListeners();

    // Save preference asynchronously in background
    _saveThemePreference();
  }

  // Set specific theme mode instantly
  void setThemeMode(ThemeMode mode) {
    final newIsDark = mode == ThemeMode.dark;
    if (_isDarkMode == newIsDark) return;

    _isDarkMode = newIsDark;
    notifyListeners();
    _saveThemePreference();
  }

  // Set dark mode directly
  void setDarkMode(bool isDark) {
    if (_isDarkMode == isDark) return;

    _isDarkMode = isDark;
    notifyListeners();
    _saveThemePreference();
  }

  // Background save operation
  void _saveThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_themePreferenceKey, _isDarkMode);
    } catch (e) {
      debugPrint('Error saving theme preference: $e');
    }
  }

  // Get colors for instant UI updates
  Color get primaryBackgroundColor => _isDarkMode ? const Color(0xFF121212) : Colors.grey[50]!;
  Color get secondaryBackgroundColor => _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;
  Color get primaryTextColor => _isDarkMode ? Colors.white : Colors.black87;
  Color get secondaryTextColor => _isDarkMode ? Colors.white70 : Colors.black54;
  Color get surfaceColor => _isDarkMode ? const Color(0xFF2D2D2D) : Colors.white;
  Color get dividerColor => _isDarkMode ? Colors.white12 : Colors.black12;
}
