import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider with ChangeNotifier {
  static const String _themePreferenceKey = 'theme_mode';
  ThemeMode _themeMode = ThemeMode.dark; // Default to dark mode

  ThemeMode get themeMode => _themeMode;

  ThemeProvider() {
    _loadThemePreference();
  }

  // Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeIndex = prefs.getInt(_themePreferenceKey);

      if (themeModeIndex != null) {
        _themeMode = ThemeMode.values[themeModeIndex];
        notifyListeners();
      }
    } catch (e) {
      // Handle error silently, keep default theme
      debugPrint('Error loading theme preference: $e');
    }
  }

  // Toggle between dark and light themes - OPTIMIZED for instant UI response
  void toggleTheme() {
    _themeMode = _themeMode == ThemeMode.dark ? ThemeMode.light : ThemeMode.dark;

    // Notify immediately for instant UI response
    notifyListeners();

    // Save to disk asynchronously without blocking UI
    _saveThemePreference();
  }

  // Set a specific theme mode - OPTIMIZED for instant UI response
  void setThemeMode(ThemeMode mode) {
    if (_themeMode == mode) return;

    _themeMode = mode;

    // Notify immediately for instant UI response
    notifyListeners();

    // Save to disk asynchronously without blocking UI
    _saveThemePreference();
  }

  // Asynchronous save operation that doesn't block UI
  void _saveThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themePreferenceKey, _themeMode.index);
    } catch (e) {
      // Handle error silently or implement retry logic
      debugPrint('Error saving theme preference: $e');
      // Could implement retry logic here if needed
    }
  }

  // Check if currently in dark mode
  bool get isDarkMode => _themeMode == ThemeMode.dark;
}
