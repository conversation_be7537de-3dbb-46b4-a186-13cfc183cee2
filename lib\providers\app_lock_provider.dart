import 'dart:async';
import 'package:flutter/material.dart';
import '../services/secure_storage_service.dart';
import '../services/auth_service.dart';

/// A provider class that manages the app lock state and settings
class AppLockProvider extends ChangeNotifier {
  final SecureStorageService _secureStorage;
  final AuthService authService; // Made public for access from settings

  bool _isInitialized = false;
  bool _isLockEnabled = false;
  bool _isBiometricEnabled = false;
  String _lockTimeout = 'immediately';
  DateTime? _lastActiveTimestamp;
  bool _isAuthenticated = false;
  bool _isArchiveLocked = false;
  bool _isArchiveAuthenticated = false;

  // Track if we are currently checking authentication state
  bool _isCheckingAuthState = false;

  // Track if the app was truly backgrounded
  bool _wasBackgrounded = false;

  AppLockProvider(this._secureStorage, this.authService);

  // App lock getters
  bool get isLockEnabled => _isLockEnabled;
  bool get isBiometricEnabled => _isBiometricEnabled;
  bool get isAuthenticated => _isAuthenticated;
  bool get isCheckingAuthState => _isCheckingAuthState;
  bool get isInitialized => _isInitialized;
  String get lockTimeout => _lockTimeout;
  
  // Archive lock getters
  bool get isArchiveLocked => _isArchiveLocked;
  bool get useAppPinForArchive => false; // For backward compatibility only
  bool get canUseArchiveBiometrics => _isBiometricEnabled;
  bool get isArchiveAuthenticated => _isArchiveAuthenticated; // Keep archive authentication separate from main app

  /// Initialize the provider by loading saved preferences
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Load lock settings
    _isLockEnabled = await _secureStorage.isLockEnabled();
    _isBiometricEnabled = await _secureStorage.isBiometricEnabled();
    _lockTimeout = await _secureStorage.getLockTimeout() ?? 'immediately';
    
    // Load archive lock settings
    _isArchiveLocked = await _secureStorage.isArchiveLocked();
    
    _isInitialized = true;
    notifyListeners();
  }

  /// Check if authentication is required based on timeout settings
  Future<bool> isAuthenticationRequired() async {
    if (!_isInitialized) await initialize();
    
    // If lock is not enabled, no authentication is required
    if (!_isLockEnabled) {
      print("DEBUG: AppLockProvider: Lock is not enabled, no authentication required");
      return false;
    }
    
    // CRITICAL SECURITY FIX: Always require authentication when the app has been backgrounded
    // This ensures consistent security in line with the user's preference for a simplified
    // security model where features work together coherently
    if (_wasBackgrounded) {
      print("DEBUG: AppLockProvider: App was backgrounded, FORCING authentication regardless of timeout");
      return true;
    }
    
    // If we're already authenticated (and not coming from background), no authentication is required
    if (_isAuthenticated) {
      print("DEBUG: AppLockProvider: Already authenticated, no re-authentication required");
      return false;
    }
    
    // For debugging only - check timeout if app has been used before
    if (_lastActiveTimestamp != null) {
      final timeout = _parseTimeoutString(_lockTimeout);
      final now = DateTime.now();
      final elapsed = now.difference(_lastActiveTimestamp!);
      
      // Log the timeout values for debugging
      print("DEBUG: AppLockProvider: Elapsed time since last active: ${elapsed.inSeconds}s, timeout: ${timeout.inSeconds}s");
      
      // If the timeout is zero, always require authentication
      if (timeout == Duration.zero) {
        print("DEBUG: AppLockProvider: Immediate lock is set, authentication required");
        return true;
      }
      
      // Check if the elapsed time exceeds timeout
      final requiresAuth = elapsed > timeout;
      print("DEBUG: AppLockProvider: Authentication required based on timeout: $requiresAuth");
      return requiresAuth;
    }
    
    // No last active timestamp, authentication is required
    print("DEBUG: AppLockProvider: No last active timestamp, authentication required");
    return true;
  }

  /// Parse the timeout string to a Duration object
  Duration _parseTimeoutString(String timeout) {
    switch (timeout) {
      case 'immediately':
        return Duration.zero;
      case '1 minute':
        return const Duration(minutes: 1);
      case '5 minutes':
        return const Duration(minutes: 5);
      case '10 minutes':
        return const Duration(minutes: 10);
      case '30 minutes':
        return const Duration(minutes: 30);
      case '1 hour':
        return const Duration(hours: 1);
      default:
        return Duration.zero; // Default to immediate lock if invalid timeout
    }
  }

  /// Authenticate using PIN
  Future<bool> authenticateWithPin(String pin) async {
    print("DEBUG: AppLockProvider: Starting PIN authentication");
    final storedPin = await _secureStorage.getPin();
    
    if (storedPin == pin) {
      // CRITICAL FIX: More robust PIN authentication handling
      print("DEBUG: AppLockProvider: PIN is correct, authenticating...");
      
      // IMPORTANT: Reset backgrounded state first to ensure proper state transition
      _wasBackgrounded = false;
      
      // Set authentication state
      _isAuthenticated = true;
      print("DEBUG: AppLockProvider: Set _isAuthenticated = $_isAuthenticated");
      
      // Update timestamp
      await _updateLastActiveTimestamp();
      
      // Force immediate UI update
      print("DEBUG: AppLockProvider: Triggering UI update after PIN authentication");
      notifyListeners();
      
      // Make sure change is detected with a small delay like we do for biometrics
      await Future.delayed(const Duration(milliseconds: 50));
      print("DEBUG: AppLockProvider: PIN Authentication state after delay: $_isAuthenticated");
      
      // Additional notification to ensure UI update
      notifyListeners();
      
      return true;
    }
    
    print("DEBUG: AppLockProvider: PIN authentication failed - incorrect PIN");
    return false;
  }

  /// Authenticate using biometrics
  Future<bool> authenticateWithBiometrics() async {
    print("DEBUG: AppLockProvider: Starting biometric authentication");
    if (!_isBiometricEnabled) {
      print("DEBUG: AppLockProvider: Biometrics not enabled in app settings");
      return false;
    }
    
    try {
      print("DEBUG: AppLockProvider: Requesting biometric authentication");
      final result = await authService.authenticateWithBiometrics(
        localizedReason: 'Unlock Dark Notes',
      );
      
      print("DEBUG: AppLockProvider: Authentication result: $result");
      
      if (result == AuthResult.success) {
        // CRITICAL FIX: More direct authentication state management
        print("DEBUG: AppLockProvider: SUCCESSFUL AUTHENTICATION DETECTED");
        
        // Set authentication state directly
        _isAuthenticated = true;
        print("DEBUG: AppLockProvider: Set _isAuthenticated = $_isAuthenticated");
        
        // Update timestamp
        await _updateLastActiveTimestamp();
        
        // Force immediate UI update
        print("DEBUG: AppLockProvider: Triggering UI update");
        notifyListeners();
        
        // Make sure change is detected
        await Future.delayed(const Duration(milliseconds: 50));
        print("DEBUG: AppLockProvider: Authentication state after delay: $_isAuthenticated");
        
        // Additional notification to ensure UI update
        notifyListeners();
        
        return true;
      } else if (result == AuthResult.canceled) {
        // User explicitly canceled - allow falling back to PIN
        print("DEBUG: AppLockProvider: Authentication was canceled by user");
        return false;
      } else {
        // Other failures
        print("DEBUG: AppLockProvider: Authentication failed: $result");
        return false;
      }
    } catch (e) {
      print('DEBUG: AppLockProvider: Authentication error: $e');
      return false;
    }
  }

  /// Force authentication (for testing or direct authentication)
  void forceAuthentication() {
    print("DEBUG: AppLockProvider: FORCE AUTHENTICATION called");
    _isAuthenticated = true;
    _updateLastActiveTimestamp();
    notifyListeners();
    print("DEBUG: AppLockProvider: Force set _isAuthenticated = $_isAuthenticated");
  }

  /// Set up a PIN for app lock
  Future<bool> setupPin(String pin) async {
    try {
      await _secureStorage.setPin(pin);
      await _secureStorage.setLockEnabled(true);
      _isLockEnabled = true;
      _isAuthenticated = true;
      await _updateLastActiveTimestamp();
      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Change the existing PIN
  Future<bool> changePin({required String currentPin, required String newPin}) async {
    // Verify current PIN first
    final isValid = await authenticateWithPin(currentPin);
    if (!isValid) return false;
    
    // Update PIN
    await _secureStorage.setPin(newPin);
    return true;
  }

  /// Remove PIN and disable app lock
  Future<bool> removePin(String pin) async {
    // Verify PIN first
    final isValid = await authenticateWithPin(pin);
    if (!isValid) return false;
    
    // Disable lock
    await _secureStorage.setLockEnabled(false);
    
    // Update state
    _isLockEnabled = false;
    notifyListeners();
    
    return true;
  }

  /// Toggle biometric authentication
  Future<bool> toggleBiometrics(bool enabled) async {
    await _secureStorage.setBiometricEnabled(enabled);
    _isBiometricEnabled = enabled;
    notifyListeners();
    return true;
  }

  /// Toggle biometric authentication with PIN verification first
  Future<bool> toggleBiometricsWithVerification(bool enabled, String pin) async {
    // Verify PIN first
    final isValid = await authenticateWithPin(pin);
    if (!isValid) return false;
    
    // Toggle biometrics
    return toggleBiometrics(enabled);
  }

  /// Check if biometric enrollment is needed
  Future<bool> isBiometricEnrollmentNeeded() async {
    if (!_isBiometricEnabled) return false;
    return _secureStorage.isBiometricEnrollmentNeeded();
  }

  /// Silently update biometric enrollment status
  /// Used when biometrics are enrolled after showing guidance
  Future<void> silentlyUpdateBiometricEnrollment() async {
    await _secureStorage.setBiometricEnrollmentNeeded(false);
  }

  /// Set the lock timeout preference
  Future<void> setLockTimeout(String timeout) async {
    await _secureStorage.setLockTimeout(timeout);
    _lockTimeout = timeout;
    notifyListeners();
  }

  /// Update the last active timestamp
  Future<void> _updateLastActiveTimestamp() async {
    _lastActiveTimestamp = DateTime.now();
    await _secureStorage.updateLastActiveTimestamp();
  }

  /// Called when app goes to background
  void appToBackground() {
    print("DEBUG: AppLockProvider: App going to background");
    _wasBackgrounded = true;
  }

  /// Enable or disable archive lock
  Future<bool> setArchiveLocked(bool enabled) async {
    await _secureStorage.setArchiveLocked(enabled);
    _isArchiveLocked = enabled;
    notifyListeners();
    return true;
  }

  /// Setup custom PIN for archive access
  Future<bool> setupArchivePin(String pin) async {
    try {
      await _secureStorage.setArchivePin(pin);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if authentication is required for archive access
  Future<bool> isArchiveAuthenticationRequired() async {
    if (!_isInitialized) await initialize();
    if (!_isArchiveLocked) return false;
    if (_isArchiveAuthenticated) return false;
    return true;
  }

  /// Authenticate archive access using PIN
  Future<bool> authenticateArchiveWithPin(String pin) async {
    // Check against archive PIN
    final storedPin = await _secureStorage.getArchivePin();
    if (storedPin == pin) {
      // ONLY set archive authentication, not main app authentication
      _isArchiveAuthenticated = true;
      // Do NOT modify main app authentication state
      print("DEBUG: Archive authenticated with PIN, main app auth state unchanged: $_isAuthenticated");
      notifyListeners();
      return true;
    }
    return false;
  }

  /// Authenticate archive using biometrics
  Future<bool> authenticateArchiveWithBiometrics() async {
    if (!_isBiometricEnabled) return false;
    
    final isAvailable = await authService.isBiometricAvailable();
    if (!isAvailable) return false;

    final result = await authService.authenticateWithBiometrics(
      localizedReason: 'Authenticate to access your archived notes',
    );
    
    final success = result == AuthResult.success;
    if (success) {
      // Only set archive authentication, not main app authentication
      _isArchiveAuthenticated = true;
      // Make sure this doesn't affect main app authentication state
      notifyListeners();
    }
    
    return success;
  }

  // We no longer use archive timestamps since we require authentication on every archive access

  /// Called when app comes to foreground
  Future<void> appToForeground() async {
    // Log for debugging
    print("DEBUG: AppLockProvider: App came to foreground, wasBackgrounded=$_wasBackgrounded");
    
    // CRITICAL FIX: Always reset ALL authentication states when returning from background
    // This ensures a completely clean slate regardless of what screens were previously used
    _isArchiveAuthenticated = false;
    _isAuthenticated = false;
    _wasBackgrounded = false;
    
    // When app returns to foreground, we ALWAYS want to check authentication
    // regardless of previous state or which screen (archive or main) was last used
    if (_isLockEnabled) {
      print("DEBUG: AppLockProvider: App lock is enabled, forcing authentication check");
      
      // Set checking state to trigger UI update
      _isCheckingAuthState = true;
      notifyListeners();
      
      // Always log the state for debugging
      print("DEBUG: AppLockProvider: GLOBAL authentication reset, app: $_isAuthenticated, archive: $_isArchiveAuthenticated");
      
      // For debugging only - we always force authentication regardless of timeout
      final needsAuth = await isAuthenticationRequired();
      print("DEBUG: AppLockProvider: Authentication would be required based on timeout: $needsAuth (but we force it anyway)");
    }
    
    // Done checking auth state - always reset this regardless of lock enabled status
    _isCheckingAuthState = false;
    
    // Always notify listeners to ensure UI updates
    // This is crucial - we must notify even if auth state didn't change
    notifyListeners();
    
    // Check if biometric enrollment status has changed
      if (_isBiometricEnabled) {
        final enrollmentNeeded = await _secureStorage.isBiometricEnrollmentNeeded();
        if (enrollmentNeeded) {
          print("DEBUG: AppLockProvider: Biometric enrollment needed");
        }
      }
  }

  /// Log out (lock the app)
  void lockApp() {
    print("DEBUG: AppLockProvider: EXPLICITLY locking app");
    _isAuthenticated = false;
    _isCheckingAuthState = false; // Make sure we're not stuck in checking state
    notifyListeners();
  }

  /// Force authentication for archive access (used when navigating back to home)
  void forceArchiveAuthentication() {
    _isArchiveAuthenticated = false;
    notifyListeners();
  }
  
  /// Reset archive authentication state when leaving archive screen
  /// This ensures users must authenticate every time they access the archive
  void resetArchiveAuthentication() {
    _isArchiveAuthenticated = false;
    notifyListeners();
  }
}
