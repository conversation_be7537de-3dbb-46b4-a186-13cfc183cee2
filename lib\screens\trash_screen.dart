import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/note.dart';
import '../providers/notes_provider.dart';
import '../utils/constants.dart';
import '../widgets/note_item.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../utils/responsive_helper.dart';

class TrashScreen extends StatefulWidget {
  const TrashScreen({super.key});

  @override
  State<TrashScreen> createState() => _TrashScreenState();
}

class _TrashScreenState extends State<TrashScreen> with SingleTickerProviderStateMixin {
  bool _isGridView = true;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    )..forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Extract plain text from the Quill Delta JSON
  String _getPlainTextFromDelta(String jsonContent) {
    try {
      // Parse the JSON string into a Dart object
      final List<dynamic> deltaJson = jsonDecode(jsonContent);

      // Extract text content from the delta operations
      String plainText = '';
      for (final op in deltaJson) {
        if (op['insert'] is String) {
          plainText += op['insert'];
        } else if (op['insert'] is Map) {
          // For embedded objects, add a placeholder
          plainText += '[attachment]';
        }
      }

      return plainText.trim();
    } catch (e) {
      // If JSON parsing fails, return the content as-is
      return jsonContent;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode ? const Color(0xFF121212) : Colors.grey[50];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trash'),
        centerTitle: false,
        backgroundColor: backgroundColor,
        elevation: 0,
        actions: [
          // View toggle button
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            tooltip:
                _isGridView ? 'Switch to list view' : 'Switch to grid view',
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          // Empty trash button
          IconButton(
            icon: const Icon(Icons.delete_forever),
            tooltip: 'Empty trash',
            onPressed: () {
              _showEmptyTrashDialog();
            },
          ),
        ],
      ),
      body: Consumer<NotesProvider>(
        builder: (context, notesProvider, child) {
          if (notesProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (notesProvider.trashedNotes.isEmpty) {
            return FadeTransition(
              opacity: _animationController,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.delete_outline,
                        size: 80,
                        color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Trash is empty',
                      style: AppTextStyles.heading2,
                    ),
                    const SizedBox(height: 12),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      child: Text(
                        'Items in trash will appear here and will be automatically deleted after 7 days',
                        style: AppTextStyles.caption,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          // Choose the appropriate view based on state
          return Column(
            children: [
              // Show trash expiration message
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: isDarkMode 
                      ? const Color(0xFF5D4037) 
                      : const Color(0xFFFFE0B2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.timer_outlined,
                      size: 20,
                      color: isDarkMode 
                          ? Colors.amber.shade300
                          : Colors.amber.shade800,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Notes in trash will be automatically deleted after 7 days',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: isDarkMode 
                              ? Colors.amber.shade100
                              : Colors.brown.shade800,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Trash contents
              Expanded(
                child: _isGridView
                    ? _buildGridView(notesProvider.trashedNotes)
                    : _buildListView(notesProvider.trashedNotes),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showEmptyTrashDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Empty Trash'),
        content: const Text(
          'Are you sure you want to permanently delete all notes in trash? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              const message = SnackBar(content: Text('Trash emptied'));
              
              Navigator.pop(context);
              Provider.of<NotesProvider>(context, listen: false)
                  .emptyTrash()
                  .then((_) {
                scaffoldMessenger.showSnackBar(message);
              });
            },
            child: const Text('Empty', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // Grid view layout for notes
  Widget _buildGridView(List<Note> notes) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: AlignedGridView.count(
        key: const ValueKey('grid_view'),
        crossAxisCount: ResponsiveHelper.getGridCrossAxisCount(context),
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
        padding: const EdgeInsets.all(12),
        itemCount: notes.length,
        itemBuilder: (context, index) {
          final note = notes[index];
          return TrashNoteItem(
            key: ValueKey('trash_note_${note.id}'),
            note: note,
            index: index,
          );
        },
      ),
    );
  }

  // List view layout for notes
  Widget _buildListView(List<Note> notes) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: ListView.builder(
        key: const ValueKey('list_view'),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        itemCount: notes.length,
        itemBuilder: (context, index) {
          final note = notes[index];
          return TrashNoteItem(
            key: ValueKey('trash_note_${note.id}'),
            note: note,
            index: index,
          );
        },
      ),
    );
  }
}

// Custom NoteItem for trash that includes restore/delete options
class TrashNoteItem extends StatelessWidget {
  final Note note;
  final int index;

  const TrashNoteItem({
    Key? key,
    required this.note,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    // Calculate days remaining until auto-deletion
    final deletedAt = note.deletedAt ?? DateTime.now();
    final deleteAfter = deletedAt.add(const Duration(days: 7));
    final now = DateTime.now();
    final daysRemaining = deleteAfter.difference(now).inDays + 1;
    
    // Determine card background color
    Color cardColor;
    if (note.themeColor != null) {
      // Convert the stored color value string back to a Color
      final colorValue = int.parse(note.themeColor!);
      final baseColor = Color(colorValue);
      // Apply the same opacity level as other screens for consistency
      cardColor = isDarkMode 
        ? baseColor.withOpacity(0.2) // Darker mode - more transparent
        : baseColor.withOpacity(0.1); // Light mode - very subtle
    } else {
      // Default card color when no theme is selected
      cardColor = isDarkMode
          ? const Color(0xFF2D2D2D)
          : Colors.white;
    }
    
    return Hero(
      tag: 'trash_note_${note.id}',
      child: AnimatedSlide(
        offset: Offset(0, index * 0.05),
        duration: Duration(milliseconds: 300 + (index * 50)),
        curve: Curves.easeOutQuint,
        child: AnimatedOpacity(
          opacity: 1.0,
          duration: Duration(milliseconds: 300 + (index * 50)),
          curve: Curves.easeOut,
          child: Card(
            elevation: 2,
            margin: const EdgeInsets.symmetric(vertical: 6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(
                color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300,
                width: 0.5,
              ),
            ),
            color: cardColor,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Note content
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        if (note.title.isNotEmpty)
                          Text(
                            note.title,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        
                        if (note.title.isNotEmpty)
                          const SizedBox(height: 8),
                        
                        // Content preview
                        Text(
                          _getPlainTextFromDelta(note.content),
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode ? Colors.white70 : Colors.black87,
                          ),
                          maxLines: 8,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        // Add space at the bottom for the overlay
                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                  
                  // Overlay with trash-specific actions
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.0),
                            Colors.black.withOpacity(0.7),
                          ],
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Row(
                          children: [
                            // Auto-delete countdown with icon
                            Icon(
                              Icons.timer_outlined,
                              size: 14,
                              color: Colors.amber.shade300,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                '$daysRemaining days left',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.amber.shade300,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            
                            // Action buttons
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Restore button
                                Material(
                                  color: Colors.transparent,
                                  borderRadius: BorderRadius.circular(20),
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(20),
                                    onTap: () => _restoreNote(context),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Icon(
                                        Icons.restore_rounded,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),
                                
                                const SizedBox(width: 4),
                                
                                // Delete permanently button
                                Material(
                                  color: Colors.transparent,
                                  borderRadius: BorderRadius.circular(20),
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(20),
                                    onTap: () => _showDeleteDialog(context),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Icon(
                                        Icons.delete_forever_rounded,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to extract plain text from Delta JSON
  String _getPlainTextFromDelta(String jsonContent) {
    try {
      // Parse the JSON string into a Dart object
      final List<dynamic> deltaJson = jsonDecode(jsonContent);

      // Extract text content from the delta operations
      String plainText = '';
      for (final op in deltaJson) {
        if (op['insert'] is String) {
          plainText += op['insert'];
        } else if (op['insert'] is Map) {
          // For embedded objects, add a placeholder
          plainText += '[attachment]';
        }
      }

      return plainText.trim();
    } catch (e) {
      // If JSON parsing fails, return the content as-is
      return jsonContent;
    }
  }

  void _restoreNote(BuildContext context) {
    if (note.id != null) {
      // Store the scaffold messenger and a message before any potential context disposal
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      const message = SnackBar(content: Text('Note restored'));
      
      Provider.of<NotesProvider>(context, listen: false)
          .restoreFromTrash(note.id!)
          .then((_) {
        // Use the stored scaffold messenger instead of looking it up after the widget might be disposed
        scaffoldMessenger.showSnackBar(message);
      });
    }
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Permanently'),
        content: const Text(
          'Are you sure you want to permanently delete this note? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Store the scaffold messenger and a message before any potential context disposal
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              const message = SnackBar(content: Text('Note permanently deleted'));
              
              Navigator.pop(context);
              if (note.id != null) {
                Provider.of<NotesProvider>(context, listen: false)
                    .deleteNotePermanently(note.id!)
                    .then((_) {
                  // Use the stored scaffold messenger
                  scaffoldMessenger.showSnackBar(message);
                });
              }
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
