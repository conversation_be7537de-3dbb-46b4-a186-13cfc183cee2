import 'package:flutter/material.dart';
import 'dart:convert';
import '../models/note.dart';
import '../utils/constants.dart';
import '../utils/date_formatter.dart';

// Custom SearchDelegate for Notes
class NotesSearchDelegate extends SearchDelegate<Note?> {
  final List<Note> notes;
  final bool isDarkMode;

  NotesSearchDelegate({required this.notes, required this.isDarkMode})
      : super(
          searchFieldLabel: 'Search notes',
          searchFieldStyle: TextStyle(
            color: isDarkMode ? Colors.white70 : Colors.black87,
            fontSize: 16,
          ),
        );

  // Extract plain text from Quill Delta JSON
  String _getPlainTextFromDelta(String jsonContent) {
    try {
      final List<dynamic> deltaJson = jsonDecode(jsonContent);
      String plainText = '';

      for (var operation in deltaJson) {
        if (operation is Map<String, dynamic> && operation.containsKey('insert')) {
          final insert = operation['insert'];
          if (insert is String) {
            plainText += insert;
          }
        }
      }

      return plainText.trim();
    } catch (e) {
      return jsonContent; // Fallback to original content if parsing fails
    }
  }

  @override
  ThemeData appBarTheme(BuildContext context) {
    final theme = Theme.of(context);
    return theme.copyWith(
      appBarTheme: AppBarTheme(
        backgroundColor:
            isDarkMode ? const Color(0xFF1F1F1F) : Colors.grey[100],
        iconTheme:
            IconThemeData(color: isDarkMode ? Colors.white : Colors.black87),
        elevation: 0,
      ),
      scaffoldBackgroundColor:
          isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
      inputDecorationTheme: InputDecorationTheme(
        hintStyle:
            TextStyle(color: isDarkMode ? Colors.white38 : Colors.black38),
        border: InputBorder.none,
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          if (query.isEmpty) {
            close(context, null);
          } else {
            query = '';
          }
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults(context);
  }

  Widget _buildSearchResults(BuildContext context) {
    if (query.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 80,
              color: isDarkMode ? Colors.white38 : Colors.black26,
            ),
            const SizedBox(height: 16),
            Text(
              'Search your notes',
              style: TextStyle(
                fontSize: 18,
                color: isDarkMode ? Colors.white70 : Colors.black54,
              ),
            ),
          ],
        ),
      );
    }

    final filteredNotes = notes.where((note) {
      final titleMatch = note.title?.toLowerCase().contains(query.toLowerCase()) ?? false;
      final contentMatch = _getPlainTextFromDelta(note.content ?? '')
          .toLowerCase()
          .contains(query.toLowerCase());
      return titleMatch || contentMatch;
    }).toList();

    if (filteredNotes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: isDarkMode ? Colors.white38 : Colors.black26,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: TextStyle(
                fontSize: 18,
                color: isDarkMode ? Colors.white70 : Colors.black54,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredNotes.length,
      itemBuilder: (context, index) {
        final note = filteredNotes[index];
        final noteColor = note.themeColor != null
            ? Color(int.parse(note.themeColor!))
            : AppColors.accent;
        final contentPreview = _getPlainTextFromDelta(note.content ?? '');

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: isDarkMode ? noteColor.withValues(alpha: 0.2) : noteColor.withValues(alpha: 0.1),
              width: 2,
            ),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            title: Text(
              note.title?.isNotEmpty == true ? note.title! : 'Untitled',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (contentPreview.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    contentPreview,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Text(
                  DateFormatter.getRelativeTime(note.updatedAt ?? DateTime.now()),
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? Colors.white38 : Colors.black38,
                  ),
                ),
              ],
            ),
            onTap: () {
              close(context, note);
            },
          ),
        );
      },
    );
  }
}
