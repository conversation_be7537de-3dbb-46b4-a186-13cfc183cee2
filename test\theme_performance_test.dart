import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:dark_notes/providers/theme_provider.dart';

void main() {
  group('Theme Performance Tests', () {
    testWidgets('Theme switching should be instant', (WidgetTester tester) async {
      final themeProvider = ThemeProvider();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeProvider,
          child: Consumer<ThemeProvider>(
            builder: (context, provider, _) {
              return MaterialApp(
                theme: ThemeData.light(),
                darkTheme: ThemeData.dark(),
                themeMode: provider.themeMode,
                themeAnimationDuration: const Duration(milliseconds: 200),
                themeAnimationCurve: Curves.easeInOut,
                home: Scaffold(
                  appBar: AppBar(
                    title: const Text('Test'),
                    backgroundColor: provider.isDarkMode
                        ? const Color(0xFF121212)
                        : Colors.grey[50],
                  ),
                  body: const Center(
                    child: Text('Theme Test'),
                  ),
                ),
              );
            },
          ),
        ),
      );

      // Verify initial state
      expect(themeProvider.isDarkMode, true); // Default is dark mode

      // Measure theme toggle performance
      final stopwatch = Stopwatch()..start();

      // Toggle theme
      themeProvider.toggleTheme();

      // Should complete immediately (synchronous)
      stopwatch.stop();
      expect(stopwatch.elapsedMilliseconds, lessThan(5)); // Should be nearly instant

      // Pump to trigger rebuild
      await tester.pump();

      // Verify theme changed
      expect(themeProvider.isDarkMode, false);

      // Test multiple rapid toggles
      for (int i = 0; i < 10; i++) {
        themeProvider.toggleTheme();
        await tester.pump(const Duration(milliseconds: 10));
      }

      // Should handle rapid toggles without issues
      expect(themeProvider.isDarkMode, false); // Should be light after odd number of toggles (10 + 1 initial = 11 total)
    });

    test('ThemeProvider should save preferences asynchronously', () async {
      final themeProvider = ThemeProvider();

      // Initial state
      expect(themeProvider.isDarkMode, true);

      // Toggle should be synchronous
      final stopwatch = Stopwatch()..start();
      themeProvider.toggleTheme();
      stopwatch.stop();

      // Should complete immediately
      expect(stopwatch.elapsedMilliseconds, lessThan(5));
      expect(themeProvider.isDarkMode, false);

      // Wait a bit for async save to complete
      await Future.delayed(const Duration(milliseconds: 100));

      // Theme should still be changed
      expect(themeProvider.isDarkMode, false);
    });

    test('setThemeMode should be synchronous', () {
      final themeProvider = ThemeProvider();

      final stopwatch = Stopwatch()..start();
      themeProvider.setThemeMode(ThemeMode.light);
      stopwatch.stop();

      // Should complete immediately
      expect(stopwatch.elapsedMilliseconds, lessThan(5));
      expect(themeProvider.themeMode, ThemeMode.light);
      expect(themeProvider.isDarkMode, false);
    });
  });
}
