import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../providers/app_lock_provider.dart';
import '../providers/notes_provider.dart';
import '../providers/theme_provider.dart';
import '../models/note.dart';
import '../utils/constants.dart';
import '../utils/date_formatter.dart';
import '../utils/navigation_utils.dart';
import 'note_detail_screen.dart';
import 'settings_screen.dart';
import 'archive_screen.dart';
import 'trash_screen.dart';
import 'dart:convert';
import 'archive_lock_screen.dart';
import '../providers/labels_provider.dart';
import '../models/label.dart';
import '../utils/responsive_helper.dart';
import '../widgets/shared_note_card.dart';
import '../widgets/label_drawer_section.dart';
import '../widgets/instant_theme_app_bar.dart';
import '../screens/label_filter_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Track current view mode (list or grid)
  bool _isGridView = true;

  // Multi-select mode state
  bool _isMultiSelectMode = false;
  final Set<int> _selectedNoteIds = {};

  @override
  void initState() {
    super.initState();
    // Fetch notes when the screen is initialized
    Future.microtask(
        () => Provider.of<NotesProvider>(context, listen: false).fetchNotes());
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Toggle selection of a note
  void _toggleNoteSelection(int noteId) {
    setState(() {
      if (_selectedNoteIds.contains(noteId)) {
        _selectedNoteIds.remove(noteId);
        // Exit multi-select mode if no notes are selected
        if (_selectedNoteIds.isEmpty) {
          _isMultiSelectMode = false;
        }
      } else {
        _selectedNoteIds.add(noteId);
      }
    });
  }

  // Enter multi-select mode with initial note selected
  void _enterMultiSelectMode(int initialNoteId) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedNoteIds.add(initialNoteId);
    });
  }

  // Exit multi-select mode
  void _exitMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = false;
      _selectedNoteIds.clear();
    });
  }

  // Update the navigation methods in the various sections

  // 1. Archive navigation in drawer
  void _navigateToArchive() async {
    final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);

    // Always reset authentication state for archive first
    // This ensures a fresh authentication check every time
    appLockProvider.resetArchiveAuthentication();

    // Now check if archive requires authentication
    final requiresAuth = await appLockProvider.isArchiveAuthenticationRequired();

    // Pop the drawer first
    Navigator.pop(context);

    if (requiresAuth) {
      // Show lock screen first and wait for result
      final authenticated = await Navigator.push(
        context,
        NavigationUtils.createSecureRoute(
          context: context,
          screen: const ArchiveLockScreen(),
        ),
      );

      // Only navigate to archive if authentication was successful
      if (authenticated == true && mounted) {
        Navigator.push(
          context,
          NavigationUtils.createSecureRoute(
            context: context,
            screen: const ArchiveScreen(),
          ),
        );
      }
    } else {
      // No authentication required, navigate directly
      Navigator.push(
        context,
        NavigationUtils.createSecureRoute(
          context: context,
          screen: const ArchiveScreen(),
        ),
      );
    }
  }

  // 2. Trash navigation in drawer
  void _navigateToTrash() {
    Navigator.pop(context);
    Navigator.push(
      context,
      NavigationUtils.createSecureRoute(
        context: context,
        screen: const TrashScreen(),
      ),
    );
  }

  // 3. Settings navigation in drawer
  void _navigateToSettings() {
    Navigator.pop(context);
    Navigator.push(
      context,
      NavigationUtils.createSecureRoute(
        context: context,
        screen: const SettingsScreen(),
      ),
    );
  }

  // 4. Note detail navigation
  void _navigateToNoteDetail(int noteId) {
    Navigator.push(
      context,
      NavigationUtils.createSecureRoute(
        context: context,
        screen: NoteDetailScreen(noteId: noteId),
      ),
    ).then((_) {
      // Refresh notes when coming back from detail screen
      Provider.of<NotesProvider>(context, listen: false).fetchNotes();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _isMultiSelectMode ? _buildMultiSelectAppBar() : _buildNormalAppBar(),
      drawer: _isMultiSelectMode ? null : Selector<ThemeProvider, bool>(
        selector: (context, themeProvider) => themeProvider.isDarkMode,
        builder: (context, isDarkMode, _) {
          final drawerBackground = isDarkMode
              ? const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1F1F1F),
                    Color(0xFF121212),
                  ],
                )
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.grey[100]!,
                    Colors.grey[50]!,
                  ],
                );

          final headerGradient = LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF303F9F),
              Color(0xFF1A237E),
            ],
          );

          final textColor = isDarkMode ? Colors.white : Colors.black87;
          final secondaryTextColor =
              isDarkMode ? Colors.white70 : Colors.black54;
          final dividerColor =
              isDarkMode ? Colors.grey[800]! : Colors.grey[300]!;

          // Check if we're in landscape mode
          final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

          return Drawer(
            backgroundColor:
                isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
            child: Container(
              decoration: BoxDecoration(gradient: drawerBackground),
              child: SafeArea(
                child: SingleChildScrollView(
                  child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Custom Header with gradient and logo
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: isLandscape ? 12 : 24,
                      ),
                      decoration: BoxDecoration(
                        gradient: headerGradient,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black26,
                            blurRadius: 8,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.notes,
                                  size: 28,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(width: 16),
                              const Text(
                                'Dark Notes',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: isLandscape ? 8 : 16),
                          Text(
                            isDarkMode
                                ? 'Capture your thoughts in the darkness'
                                : 'Organize your thoughts with clarity',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 14,
                              fontWeight: FontWeight.w300,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Menu Items Section
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'MENU',
                        style: TextStyle(
                          color: secondaryTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.2,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildMenuTile(
                      icon: Icons.description_outlined,
                      title: 'Notes',
                      isActive: true,
                      textColor: textColor,
                      inactiveColor: secondaryTextColor,
                      onTap: () {
                        Navigator.pop(context);
                      },
                    ),
                    _buildMenuTile(
                      icon: Icons.archive_outlined,
                      title: 'Archive',
                      textColor: textColor,
                      inactiveColor: secondaryTextColor,
                      onTap: _navigateToArchive,
                    ),
                    _buildMenuTile(
                      icon: Icons.delete_outline,
                      title: 'Trash',
                      textColor: textColor,
                      inactiveColor: secondaryTextColor,
                      onTap: _navigateToTrash,
                    ),
                    SizedBox(height: isLandscape ? 8 : 16),

                    // Labels section
                    const LabelDrawerSection(),

                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Divider(
                        color: dividerColor,
                        thickness: 1,
                      ),
                    ),
                    SizedBox(height: isLandscape ? 8 : 16),

                    // Settings Section
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'PREFERENCES',
                        style: TextStyle(
                          color: secondaryTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.2,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildMenuTile(
                      icon: Icons.settings_outlined,
                      title: 'Settings',
                      textColor: textColor,
                      inactiveColor: secondaryTextColor,
                      onTap: _navigateToSettings,
                    ),
                    _buildMenuTile(
                      icon: Icons.help_outline,
                      title: 'Help & Feedback',
                      textColor: textColor,
                      inactiveColor: secondaryTextColor,
                      onTap: () {
                        Navigator.pop(context);
                        // TODO: Navigate to help screen
                      },
                    ),
                    SizedBox(height: isLandscape ? 16 : 40),

                    // App Version
                    Center(
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 24),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: (isDarkMode ? Colors.white : Colors.black)
                              .withOpacity(0.05),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Version 1.0',
                          style: TextStyle(
                            color: secondaryTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                ),
              ),
            ),
          );
        },
      ),
      body: Consumer<NotesProvider>(
        builder: (context, notesProvider, _) {
          final pinnedNotes = notesProvider.pinnedNotes;
          final unpinnedNotes = notesProvider.notes;

          if (notesProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (pinnedNotes.isEmpty && unpinnedNotes.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.note_alt_outlined,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No notes yet',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Tap the + button to create a note',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return CustomScrollView(
            slivers: [
              // Pinned notes section
              if (pinnedNotes.isNotEmpty) ...[
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Row(
                      children: [
                        const Text(
                          'PINNED',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                            letterSpacing: 1.0,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Container(
                          height: 1,
                          width: 32,
                          color: Colors.grey.withOpacity(0.5),
                        ),
                      ],
                    ),
                  ),
                ),
                _isGridView
                    ? SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: _buildGridView(pinnedNotes),
                        ),
                      )
                    : SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final note = pinnedNotes[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 3.0, horizontal: 4.0),
                              child: SharedNoteCard(
                                note: note,
                                isSelected: _selectedNoteIds.contains(note.id),
                                isInSelectionMode: _isMultiSelectMode,
                                onLongPress: note.id != null ? () => _enterMultiSelectMode(note.id!) : null,
                                onTap: () {
                                  if (_isMultiSelectMode) {
                                    if (note.id != null) {
                                      _toggleNoteSelection(note.id!);
                                    }
                                  } else {
                                    _navigateToNoteDetail(note.id!);
                                  }
                                },
                                onPinTap: () {
                                  if (note.id != null) {
                                    Provider.of<NotesProvider>(context, listen: false)
                                        .toggleNotePin(note.id!);
                                  }
                                },
                              ),
                            );
                          },
                          childCount: pinnedNotes.length,
                        ),
                      ),
              ],

              // Unpinned notes section
              if (unpinnedNotes.isNotEmpty) ...[
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Row(
                      children: [
                        Text(
                          pinnedNotes.isNotEmpty ? 'OTHERS' : '',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                            letterSpacing: 1.0,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Container(
                          height: 1,
                          width: 32,
                          color: Colors.grey.withOpacity(0.5),
                        ),
                      ],
                    ),
                  ),
                ),
                _isGridView
                    ? SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 80.0), // Add padding for FAB
                          child: _buildGridView(unpinnedNotes),
                        ),
                      )
                    : SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final note = unpinnedNotes[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 3.0, horizontal: 4.0),
                              child: SharedNoteCard(
                              note: note,
                              isSelected: _selectedNoteIds.contains(note.id),
                              isInSelectionMode: _isMultiSelectMode,
                              onLongPress: note.id != null ? () => _enterMultiSelectMode(note.id!) : null,
                              onTap: () {
                                if (_isMultiSelectMode) {
                                  if (note.id != null) {
                                    _toggleNoteSelection(note.id!);
                                  }
                                } else {
                                  _navigateToNoteDetail(note.id!);
                                }
                              },
                              onPinTap: () {
                                if (note.id != null) {
                                  Provider.of<NotesProvider>(context, listen: false)
                                      .toggleNotePin(note.id!);
                                }
                              },
                              ),
                            );
                          },
                          childCount: unpinnedNotes.length,
                        ),
                      ),
              ],

              // Add padding at the bottom for FAB
              if (!_isGridView)
                const SliverToBoxAdapter(
                  child: SizedBox(height: 80),
                ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            NavigationUtils.createSecureRoute(
              context: context,
              screen: const NoteDetailScreen(),
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showNoteOptions(Note note) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final noteColor = note.themeColor != null
        ? Color(int.parse(note.themeColor!))
        : AppColors.accent;

    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Small handle at top of sheet
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Note title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                note.title.isEmpty ? 'Untitled Note' : note.title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: noteColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 20),

            // Option: Edit
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Note'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  NavigationUtils.createSecureRoute(
                    context: context,
                    screen: NoteDetailScreen(noteId: note.id),
                  ),
                );
              },
            ),

            // Option: Archive
            ListTile(
              leading: const Icon(Icons.archive_outlined),
              title: const Text('Archive Note'),
              onTap: () async {
                Navigator.pop(context);
                await Provider.of<NotesProvider>(context, listen: false)
                    .archiveNote(note.id!);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Note archived')),
                  );
                }
              },
            ),

            // Option: Delete
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: const Text('Delete Note', style: TextStyle(color: Colors.red)),
              onTap: () async {
                Navigator.pop(context);
                await Provider.of<NotesProvider>(context, listen: false)
                    .moveToTrash(note.id!);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Note moved to trash')),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuTile({
    required IconData icon,
    required String title,
    bool isActive = false,
    Color? textColor,
    Color? inactiveColor,
    VoidCallback? onTap,
  }) {
    final effectiveTextColor = isActive
        ? textColor ?? Colors.white
        : inactiveColor ?? Colors.grey[400];

    return ListTile(
      leading: Icon(
        icon,
        size: 24,
        color: effectiveTextColor,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: effectiveTextColor,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 24),
      dense: true,
      visualDensity: const VisualDensity(vertical: -1),
      onTap: onTap,
    );
  }

  Widget _buildLabelsList() {
    return Consumer<LabelsProvider>(
      builder: (context, labelsProvider, child) {
        if (labelsProvider.isLoading) {
          return const ListTile(
            title: Text('Loading labels...'),
          );
        }

        final labels = labelsProvider.labels;

        if (labels.isEmpty) {
          return const ListTile(
            title: Text('No labels yet'),
            dense: true,
            enabled: false,
          );
        }

        return Column(
          children: labels.map((label) => _buildLabelTile(label)).toList(),
        );
      },
    );
  }

  Widget _buildLabelTile(Label label) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: label.color,
        radius: 12,
      ),
      title: Text(label.name),
      dense: true,
      onTap: () {
        // Close the drawer
        Navigator.pop(context);
        // Navigate to the label's notes screen
        Navigator.push(
          context,
          NavigationUtils.createSecureRoute(
            context: context,
            screen: LabelFilterScreen(labelId: label.id!),
          ),
        );
      },
    );
  }

  Widget _buildGridView(List<Note> notes) {
    return Container(
      padding: const EdgeInsets.all(6.0), // Slightly more padding around the grid
      child: MasonryGridView.count(
        crossAxisCount: ResponsiveHelper.getGridCrossAxisCount(context),
        mainAxisSpacing: 8.0,  // Increased spacing for better visual separation
        crossAxisSpacing: 8.0, // Increased spacing for better visual separation
        itemCount: notes.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        // Use masonry grid for better content fit
        itemBuilder: (context, index) {
          final note = notes[index];
          return SharedNoteCard(
            note: note,
            isSelected: _selectedNoteIds.contains(note.id),
            isInSelectionMode: _isMultiSelectMode,
            onLongPress: note.id != null ? () => _enterMultiSelectMode(note.id!) : null,
            onTap: () {
              if (_isMultiSelectMode) {
                if (note.id != null) {
                  _toggleNoteSelection(note.id!);
                }
              } else {
                _navigateToNoteDetail(note.id!);
              }
            },
            onPinTap: () {
              if (note.id != null) {
                Provider.of<NotesProvider>(context, listen: false)
                    .toggleNotePin(note.id!);
              }
            },
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildNormalAppBar() {
    return InstantThemeAppBar(
      title: 'Dark Notes',
      actions: [
        // View toggle button
        IconButton(
          icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
          tooltip: _isGridView ? 'Switch to list view' : 'Switch to grid view',
          onPressed: () {
            setState(() {
              _isGridView = !_isGridView;
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            final notesProvider = Provider.of<NotesProvider>(context, listen: false);
            final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

            showSearch(
              context: context,
              delegate: NotesSearchDelegate(
                notes: [...notesProvider.pinnedNotes, ...notesProvider.notes],
                isDarkMode: themeProvider.isDarkMode,
              ),
            ).then((selectedNote) {
              if (selectedNote != null && mounted) {
                Navigator.push(
                  context,
                  NavigationUtils.createSecureRoute(
                    context: context,
                    screen: NoteDetailScreen(noteId: selectedNote.id),
                  ),
                );
              }
            });
          },
        ),
      ],
    );
  }

  PreferredSizeWidget _buildMultiSelectAppBar() {
    return InstantThemeMultiSelectAppBar(
      selectedCount: _selectedNoteIds.length,
      onClose: _exitMultiSelectMode,
      onPin: _togglePinSelectedNotes,
      onColor: _showColorPicker,
      onArchive: _archiveSelectedNotes,
      onDelete: _deleteSelectedNotes,
      onMoreAction: _handleMoreMenuAction,
    );
  }

  // Toggle pin status for selected notes
  void _togglePinSelectedNotes() async {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);

    for (final noteId in _selectedNoteIds) {
      await notesProvider.toggleNotePin(noteId);
    }

    _exitMultiSelectMode();
  }

  // Archive selected notes
  void _archiveSelectedNotes() async {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);

    for (final noteId in _selectedNoteIds) {
      await notesProvider.archiveNote(noteId);
    }

    _exitMultiSelectMode();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${_selectedNoteIds.length} notes archived'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // Show color picker for selected notes
  void _showColorPicker() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Define color options
    final List<Color> colorOptions = [
      Colors.white,
      Colors.red.shade200,
      Colors.orange.shade200,
      Colors.yellow.shade200,
      Colors.green.shade200,
      Colors.blue.shade200,
      Colors.purple.shade200,
      Colors.pink.shade200,
      Colors.brown.shade200,
      Colors.grey.shade300,
    ];

    if (isDarkMode) {
      // Darker variants for dark mode
      colorOptions.replaceRange(0, colorOptions.length, [
        const Color(0xFF2D2D2D), // Dark grey (default)
        Colors.red.shade900,
        Colors.orange.shade900,
        Colors.amber.shade900,
        Colors.green.shade900,
        Colors.blue.shade900,
        Colors.purple.shade900,
        Colors.pink.shade900,
        Colors.brown.shade900,
        Colors.grey.shade800,
      ]);
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0, bottom: 16.0),
              child: Text(
                'Note color',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 5,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemCount: colorOptions.length,
              itemBuilder: (context, index) {
                return InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    _applyColorToSelectedNotes(colorOptions[index]);
                  },
                  borderRadius: BorderRadius.circular(50),
                  child: Container(
                    decoration: BoxDecoration(
                      color: colorOptions[index],
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isDarkMode ? Colors.white30 : Colors.black12,
                        width: 1,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // Apply color to selected notes
  void _applyColorToSelectedNotes(Color color) async {
    // Convert color to string format that can be stored in the database
    final colorValue = color.value.toString();

    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    await notesProvider.updateNotesColor(_selectedNoteIds, colorValue);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Color applied to ${_selectedNoteIds.length} notes'),
        duration: const Duration(seconds: 2),
      ),
    );

    _exitMultiSelectMode();
  }

  // Handle more menu actions
  void _handleMoreMenuAction(String action) {
    switch (action) {
      case 'labels':
        _showLabelsDialog();
        break;
      case 'delete':
        _deleteSelectedNotes();
        break;
    }
  }

  // Show labels dialog
  void _showLabelsDialog() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Column(
          children: [
            // Handle and title
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Add label',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
            ),

            // Label list
            Expanded(
              child: Consumer<LabelsProvider>(
                builder: (context, labelsProvider, _) {
                  if (labelsProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (labelsProvider.labels.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.label_outline,
                            size: 56,
                            color: isDarkMode ? Colors.white38 : Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No labels yet',
                            style: TextStyle(
                              fontSize: 16,
                              color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.add),
                            label: const Text('Create new label'),
                            onPressed: () {
                              // TODO: Implement create new label functionality
                              Navigator.pop(context);
                            },
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    controller: scrollController,
                    itemCount: labelsProvider.labels.length + 1, // +1 for "Create new label" option
                    itemBuilder: (context, index) {
                      if (index == labelsProvider.labels.length) {
                        // "Create new label" option at the end
                        return ListTile(
                          leading: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(Icons.add, size: 18),
                          ),
                          title: const Text('Create new label'),
                          onTap: () {
                            // TODO: Implement create new label functionality
                            Navigator.pop(context);
                          },
                        );
                      }

                      final label = labelsProvider.labels[index];
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: label.color,
                          radius: 12,
                        ),
                        title: Text(label.name),
                        onTap: () {
                          _addLabelToSelectedNotes(label.id!);
                          Navigator.pop(context);
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Add label to selected notes
  void _addLabelToSelectedNotes(int labelId) async {
    final labelsProvider = Provider.of<LabelsProvider>(context, listen: false);
    await labelsProvider.addLabelToMultipleNotes(labelId, _selectedNoteIds);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Label added to ${_selectedNoteIds.length} notes'),
        duration: const Duration(seconds: 2),
      ),
    );

    _exitMultiSelectMode();
  }

  // Delete selected notes
  void _deleteSelectedNotes() async {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    final count = _selectedNoteIds.length;

    for (final noteId in _selectedNoteIds) {
      await notesProvider.moveToTrash(noteId);
    }

    _exitMultiSelectMode();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$count notes moved to trash'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

// Custom SearchDelegate for Notes
class NotesSearchDelegate extends SearchDelegate<Note?> {
  final List<Note> notes;
  final bool isDarkMode;

  NotesSearchDelegate({required this.notes, required this.isDarkMode})
      : super(
          searchFieldLabel: 'Search notes',
          searchFieldStyle: TextStyle(
            color: isDarkMode ? Colors.white70 : Colors.black87,
            fontSize: 16,
          ),
        );

  // Extract plain text from Quill Delta JSON
  String _getPlainTextFromDelta(String jsonContent) {
    try {
      final List<dynamic> deltaJson = jsonDecode(jsonContent);
      String plainText = '';
      for (final op in deltaJson) {
        if (op['insert'] is String) {
          plainText += op['insert'];
        } else if (op['insert'] is Map) {
          plainText += '[attachment]';
        }
      }
      return plainText.trim();
    } catch (e) {
      return jsonContent;
    }
  }

  @override
  ThemeData appBarTheme(BuildContext context) {
    final theme = Theme.of(context);
    return theme.copyWith(
      appBarTheme: AppBarTheme(
        backgroundColor:
            isDarkMode ? const Color(0xFF1F1F1F) : Colors.grey[100],
        iconTheme:
            IconThemeData(color: isDarkMode ? Colors.white : Colors.black87),
        elevation: 0,
      ),
      scaffoldBackgroundColor:
          isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
      inputDecorationTheme: InputDecorationTheme(
        hintStyle:
            TextStyle(color: isDarkMode ? Colors.white38 : Colors.black38),
        border: InputBorder.none,
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          if (query.isEmpty) {
            close(context, null);
          } else {
            query = '';
          }
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults(context);
  }

  Widget _buildSearchResults(BuildContext context) {
    if (query.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 80,
              color: isDarkMode ? Colors.white38 : Colors.black26,
            ),
            const SizedBox(height: 16),
            Text(
              'Search your notes',
              style: TextStyle(
                fontSize: 18,
                color: isDarkMode ? Colors.white70 : Colors.black54,
              ),
            ),
          ],
        ),
      );
    }

    final filteredNotes = notes.where((note) {
      final titleMatch = note.title.toLowerCase().contains(query.toLowerCase());
      final contentMatch = _getPlainTextFromDelta(note.content)
          .toLowerCase()
          .contains(query.toLowerCase());
      return titleMatch || contentMatch;
    }).toList();

    if (filteredNotes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: isDarkMode ? Colors.white38 : Colors.black26,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: TextStyle(
                fontSize: 18,
                color: isDarkMode ? Colors.white70 : Colors.black54,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredNotes.length,
      itemBuilder: (context, index) {
        final note = filteredNotes[index];
        final noteColor = note.themeColor != null
            ? Color(int.parse(note.themeColor!))
            : AppColors.accent;
        final contentPreview = _getPlainTextFromDelta(note.content);

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: isDarkMode ? noteColor.withOpacity(0.2) : noteColor.withOpacity(0.1),
              width: 2,
            ),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            title: Text(
              note.title.isEmpty ? 'Untitled' : note.title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (contentPreview.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    contentPreview,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Text(
                  DateFormatter.getRelativeTime(note.updatedAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? Colors.white38 : Colors.black38,
                  ),
                ),
              ],
            ),
            onTap: () {
              close(context, note);
            },
          ),
        );
      },
    );
  }
}

