import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_lock_provider.dart';
import '../utils/constants.dart';
import '../widgets/pin_screen.dart';
import '../widgets/biometric_setup_dialog.dart';
import '../widgets/biometric_guidance_dialog.dart';
import '../services/auth_service.dart';

class AppLockSettingsScreen extends StatefulWidget {
  const AppLockSettingsScreen({Key? key}) : super(key: key);

  @override
  State<AppLockSettingsScreen> createState() => _AppLockSettingsScreenState();
}

class _AppLockSettingsScreenState extends State<AppLockSettingsScreen> {
  final List<String> _timeoutOptions = [
    'immediately',
    '1 minute',
    '5 minutes',
    '10 minutes',
    '30 minutes',
    '1 hour',
  ];

  // Track dark mode state
  bool isDarkMode = false;
  
  @override
  Widget build(BuildContext context) {
    isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text('App Lock'),
        elevation: 0,
      ),
      body: Consumer<AppLockProvider>(
        builder: (context, appLockProvider, _) {
          return ListView(
            children: [
              // Enable App Lock
              _buildSectionHeader('Security'),
              _buildToggleOption(
                icon: Icons.lock_outline,
                title: 'Enable app lock',
                subtitle: 'Lock the app with a password or PIN',
                value: appLockProvider.isLockEnabled,
                onChanged: (value) {
                  if (value) {
                    // Show PIN setup screen
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PinScreen(
                          title: 'Create a PIN',
                          subtitle: 'Set a PIN to secure your notes',
                          hideVisibilityToggle: true, // Hide the eye button for cleaner UI
                          onPinSubmitted: (pin) async {
                            // Instead of setting up PIN immediately, navigate to confirmation screen
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PinScreen(
                                  title: 'Confirm PIN',
                                  subtitle: 'Re-enter your PIN to confirm',
                                  hideVisibilityToggle: true, // Hide the eye button for cleaner UI
                                  onPinSubmitted: (confirmPin) async {
                                    if (pin == confirmPin) {
                                      // PINs match, proceed with setup
                                      final success = await appLockProvider.setupPin(pin);
                                      if (success && mounted) {
                                        // Pop both screens to return to settings
                                        Navigator.pop(context);
                                        Navigator.pop(context);
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          const SnackBar(
                                            content: Text('PIN set successfully'),
                                          ),
                                        );
                                      }
                                    } else {
                                      // PINs don't match, show error
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        const SnackBar(
                                          content: Text('PINs don\'t match. Please try again.'),
                                        ),
                                      );
                                      // Go back to first PIN entry
                                      Navigator.pop(context);
                                    }
                                  },
                                ),
                                fullscreenDialog: true,
                              ),
                            );
                          },
                        ),
                        fullscreenDialog: true,
                      ),
                    );
                  } else {
                    // Show PIN verification to disable
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PinScreen(
                          title: 'Verify PIN',
                          subtitle: 'Enter your PIN to disable app lock',
                          hideVisibilityToggle: true, // Hide the eye button for cleaner UI
                          onPinSubmitted: (pin) async {
                            final success = await appLockProvider.removePin(pin);
                            if (success && mounted) {
                              Navigator.pop(context);
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('App lock disabled'),
                                ),
                              );
                            } else if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Incorrect PIN'),
                                ),
                              );
                            }
                          },
                        ),
                        fullscreenDialog: true,
                      ),
                    );
                  }
                },
              ),

              // App Lock Timeout - only show if app lock is enabled
              if (appLockProvider.isLockEnabled) ...[
                _buildSectionHeader('Lock Timeout'),
                _buildDropdownOption(
                  icon: Icons.timer_outlined,
                  title: 'App lock timeout',
                  subtitle: 'Automatically lock the app after a certain period',
                  options: _timeoutOptions,
                  value: appLockProvider.lockTimeout,
                  onChanged: (value) {
                    if (value != null) {
                      appLockProvider.setLockTimeout(value);
                    }
                  },
                ),
              ],

              // Change PIN - only show if app lock is enabled
              if (appLockProvider.isLockEnabled) ...[
                _buildSectionHeader('PIN Management'),
                _buildActionOption(
                  icon: Icons.password,
                  title: 'Change app lock PIN',
                  subtitle: 'Set up a new password for app lock',
                  onTap: () {
                    // For PIN change, we need a two-step flow
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PinScreen(
                          title: 'Enter Current PIN',
                          subtitle: 'Enter your current PIN before creating a new one',
                          onPinSubmitted: (currentPin) async {
                            final isValidPin = await appLockProvider.authenticateWithPin(currentPin);
                            
                            if (isValidPin) {
                              if (mounted) {
                                // Navigate to second screen to set new PIN
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => PinScreen(
                                      title: 'Set New PIN',
                                      subtitle: 'Create a new PIN for your app',
                                      onPinSubmitted: (newPin) async {
                                        // Add confirmation step for the new PIN
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => PinScreen(
                                              title: 'Confirm New PIN',
                                              subtitle: 'Re-enter your new PIN to confirm',
                                              onPinSubmitted: (confirmPin) async {
                                                if (newPin == confirmPin) {
                                                  // PINs match, proceed with change
                                                  final success = await appLockProvider.changePin(
                                                    currentPin: currentPin,
                                                    newPin: newPin,
                                                  );
                                                  
                                                  if (success && mounted) {
                                                    // Pop all three screens to go back to settings
                                                    Navigator.pop(context);
                                                    Navigator.pop(context);
                                                    Navigator.pop(context);
                                                    
                                                    ScaffoldMessenger.of(context).showSnackBar(
                                                      const SnackBar(
                                                        content: Text('PIN changed successfully'),
                                                      ),
                                                    );
                                                  }
                                                } else {
                                                  // PINs don't match, show error
                                                  ScaffoldMessenger.of(context).showSnackBar(
                                                    const SnackBar(
                                                      content: Text('PINs don\'t match. Please try again.'),
                                                    ),
                                                  );
                                                  // Go back to first PIN entry
                                                  Navigator.pop(context);
                                                }
                                              },
                                            ),
                                            fullscreenDialog: true,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                );
                              }
                            } else if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Incorrect current PIN'),
                                ),
                              );
                            }
                          },
                        ),
                        fullscreenDialog: true,
                      ),
                    );
                  },
                ),
                
                // Remove PIN
                _buildActionOption(
                  icon: Icons.delete_outline,
                  title: 'Remove app lock PIN',
                  subtitle: 'Remove PIN, app lock will be disabled if no other security method is enabled',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PinScreen(
                          title: 'Verify PIN',
                          subtitle: 'Enter your PIN to remove it',
                          onPinSubmitted: (pin) async {
                            final success = await appLockProvider.removePin(pin);
                            if (success && mounted) {
                              Navigator.pop(context);
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('PIN removed and app lock disabled'),
                                ),
                              );
                            } else if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Incorrect PIN'),
                                ),
                              );
                            }
                          },
                        ),
                        fullscreenDialog: true,
                      ),
                    );
                  },
                ),
              ],

              // Biometric Authentication - only show if app lock is enabled and device supports biometrics
              if (appLockProvider.isLockEnabled) ...[
                FutureBuilder<bool>(
                  future: Provider.of<AppLockProvider>(context, listen: false)
                      .authService
                      .isBiometricAvailable(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData && snapshot.data == true) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionHeader('Biometric Authentication'),
                          _buildToggleOption(
                            icon: Icons.fingerprint,
                            title: 'Unlock with biometrics',
                            subtitle: 'Unlock the app with biometric authentication. This requires biometrics to be enabled on your device.',
                            value: appLockProvider.isBiometricEnabled,
                            onChanged: (value) async {
                              if (value) {
                                // Enabling biometrics - first check enrollment status
                                final biometricStatus = await appLockProvider.authService.getBiometricStatus();
                                
                                if (biometricStatus == BiometricStatus.unsupported) {
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Your device doesn\'t support biometric authentication'),
                                      ),
                                    );
                                  }
                                  return;
                                }
                                
                                // Show PIN verification dialog
                                if (mounted) {
                                  showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (context) => BiometricSetupDialog(
                                      onPinSubmitted: (pin) async {
                                        // Verify PIN and enable biometrics
                                        final result = await appLockProvider.toggleBiometricsWithVerification(value, pin);
                                        
                                        if (result) {
                                          // Check if biometrics need enrollment after successful toggle
                                          final needsEnrollment = await appLockProvider.isBiometricEnrollmentNeeded();
                                          
                                          if (needsEnrollment && mounted) {
                                            showDialog(
                                              context: context,
                                              builder: (context) => BiometricGuidanceDialog(
                                                onDismiss: () async {
                                                  // Check if biometrics were enrolled during the guidance
                                                  // If so, update the status silently
                                                  final newStatus = await appLockProvider.authService.getBiometricStatus();
                                                  if (newStatus == BiometricStatus.enrolled) {
                                                    await appLockProvider.silentlyUpdateBiometricEnrollment();
                                                  }
                                                },
                                              ),
                                            );
                                          }
                                        } else {
                                          // Show error message
                                          if (mounted) {
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              const SnackBar(
                                                content: Text('Failed to enable biometric authentication. Incorrect PIN.'),
                                              ),
                                            );
                                          }
                                        }
                                      },
                                      onCancel: () {
                                        // Reset the toggle if canceled
                                        setState(() {});
                                      },
                                    ),
                                  );
                                }
                              } else {
                                // Simply disable biometrics without verification
                                await appLockProvider.toggleBiometrics(value);
                              }
                            },
                          ),
                          
                          // Add a section to check biometric enrollment if needed
                          FutureBuilder<bool>(
                            future: appLockProvider.isBiometricEnrollmentNeeded(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData && snapshot.data == true && appLockProvider.isBiometricEnabled) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                                  child: Card(
                                    color: Theme.of(context).colorScheme.errorContainer,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.info_outline,
                                                color: Theme.of(context).colorScheme.onErrorContainer,
                                              ),
                                              const SizedBox(width: 12),
                                              Expanded(
                                                child: Text(
                                                  'Biometric setup incomplete',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Theme.of(context).colorScheme.onErrorContainer,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            'You need to set up biometrics in your device settings to use this feature.',
                                            style: TextStyle(
                                              color: Theme.of(context).colorScheme.onErrorContainer,
                                            ),
                                          ),
                                          const SizedBox(height: 12),
                                          TextButton(
                                            style: TextButton.styleFrom(
                                              foregroundColor: Theme.of(context).colorScheme.onErrorContainer,
                                              backgroundColor: Theme.of(context).colorScheme.errorContainer.withOpacity(0.3),
                                            ),
                                            onPressed: () {
                                              showDialog(
                                                context: context,
                                                builder: (context) => BiometricGuidanceDialog(
                                                  onDismiss: () async {
                                                    // Check if biometrics were enrolled during the guidance
                                                    final newStatus = await appLockProvider.authService.getBiometricStatus();
                                                    if (newStatus == BiometricStatus.enrolled) {
                                                      await appLockProvider.silentlyUpdateBiometricEnrollment();
                                                      setState(() {});
                                                    }
                                                  },
                                                ),
                                              );
                                            },
                                            child: Text('SHOW SETUP INSTRUCTIONS'),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                        ],
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
              
              // Archive Lock Section
              _buildSectionHeader('Archive Lock'),
              _buildToggleOption(
                icon: Icons.archive_outlined,
                title: 'Enable archive lock',
                subtitle: 'Require authentication to access archived notes',
                value: appLockProvider.isArchiveLocked,
                onChanged: (value) async {
                  // If enabling archive lock
                  if (value) {
                    // Always require setting a dedicated PIN for archive
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PinScreen(
                          title: 'Set Archive PIN',
                          subtitle: 'This PIN will be used to access your archived notes',
                          onPinSubmitted: (pin) async {
                            // Always require confirmation step
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PinScreen(
                                  title: 'Confirm Archive PIN',
                                  subtitle: 'Re-enter your PIN to confirm',
                                  onPinSubmitted: (confirmPin) async {
                                    if (pin == confirmPin) {
                                      // PINs match, proceed with setup
                                      final success = await appLockProvider.setupArchivePin(pin);
                                      if (success && mounted) {
                                        await appLockProvider.setArchiveLocked(true);
                                        // Pop both screens
                                        Navigator.pop(context);
                                        Navigator.pop(context);
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          const SnackBar(
                                            content: Text('Archive PIN set successfully'),
                                          ),
                                        );
                                      }
                                    } else {
                                      // PINs don't match, show error
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        const SnackBar(
                                          content: Text('PINs don\'t match. Please try again.'),
                                        ),
                                      );
                                      // Go back to first PIN entry
                                      Navigator.pop(context);
                                    }
                                  },
                                ),
                                fullscreenDialog: true,
                              ),
                            );
                          },
                        ),
                        fullscreenDialog: true,
                      ),
                    );
                  } else {
                    // First show a confirmation dialog to ensure the user understands the implications
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Disable Archive Lock?'),
                        content: const Text(
                          'Disabling archive lock will make your archived notes accessible without ' +
                          'verification. Anyone with access to your device will be able to view your archived notes.\n\n' +
                          'For security reasons, you must verify your identity to disable this feature.'
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('CANCEL'),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                              
                              // If biometrics enabled, offer that option first
                              if (appLockProvider.canUseArchiveBiometrics) {
                                showDialog(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: const Text('Verification Method'),
                                    content: const Text('How would you like to verify your identity?'),
                                    actions: [
                                      TextButton(
                                        onPressed: () async {
                                          Navigator.pop(context);
                                          // Try biometric authentication
                                          final result = await appLockProvider.authenticateArchiveWithBiometrics();
                                          if (result && mounted) {
                                            // Success, disable archive lock
                                            await appLockProvider.setArchiveLocked(false);
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              const SnackBar(
                                                content: Text('Archive lock disabled'),
                                              ),
                                            );
                                          } else if (mounted) {
                                            // Failed or canceled
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              const SnackBar(
                                                content: Text('Verification failed. Archive lock not disabled.'),
                                              ),
                                            );
                                          }
                                        },
                                        child: const Text('BIOMETRICS'),
                                      ),
                                      TextButton(
                                        onPressed: () {
                                          Navigator.pop(context);
                                          _verifyWithPin(appLockProvider);
                                        },
                                        child: const Text('PIN CODE'),
                                      ),
                                    ],
                                  ),
                                );
                              } else {
                                // No biometrics, go straight to PIN verification
                                _verifyWithPin(appLockProvider);
                              }
                            },
                            child: const Text('CONTINUE'),
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),

              // Archive PIN Management - only show if archive lock is enabled
              if (appLockProvider.isArchiveLocked) ...[
                _buildActionOption(
                  icon: Icons.password,
                  title: 'Change archive PIN',
                  subtitle: 'Set a new PIN for accessing archived notes',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PinScreen(
                          title: 'Enter Current PIN',
                          subtitle: 'Enter your current PIN before creating a new one',
                          onPinSubmitted: (currentPin) async {
                            final isValidPin = await appLockProvider.authenticateArchiveWithPin(currentPin);
                            
                            if (isValidPin) {
                              if (mounted) {
                                // Navigate to second screen to set new PIN
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => PinScreen(
                                      title: 'Set New PIN',
                                      subtitle: 'Create a new PIN for your archived notes',
                                      onPinSubmitted: (newPin) async {
                                        // First verify current PIN
                                        final isValid = await appLockProvider.authenticateArchiveWithPin(currentPin);
                                        if (isValid) {
                                          // If valid, set new PIN
                                          final success = await appLockProvider.setupArchivePin(newPin);
                                          
                                          if (success && mounted) {
                                            Navigator.pop(context);
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              const SnackBar(
                                                content: Text('Archive PIN changed successfully'),
                                              ),
                                            );
                                          }
                                        } else if (mounted) {
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            const SnackBar(
                                              content: Text('Incorrect current PIN'),
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                  ),
                                );
                              }
                            } else if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Incorrect current PIN'),
                                ),
                              );
                            }
                          },
                        ),
                        fullscreenDialog: true,
                      ),
                    );
                  },
                ),
              ],

              // Option to use app PIN - only show if archive lock is enabled and app lock is enabled
              // Archive now always uses a separate PIN

              const SizedBox(height: 32),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    final textColor = isDarkMode ? Colors.white70 : Colors.black54;
    
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        top: 24,
        right: 16,
        bottom: 8,
      ),
      child: Text(
        title.toUpperCase(),
        style: AppTextStyles.caption.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
          letterSpacing: 1.2,
        ),
      ),
    );
  }

  Widget _buildToggleOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.accent.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.accent,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.subtitle.copyWith(
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.caption.copyWith(
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.accent,
      ),
    );
  }

  Widget _buildActionOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.accent.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.accent,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.subtitle.copyWith(
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.caption.copyWith(
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      onTap: onTap,
    );
  }

  Widget _buildDropdownOption<T>({
    required IconData icon,
    required String title,
    required String subtitle,
    required List<T> options,
    required T value,
    required ValueChanged<T?> onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.accent.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.accent,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.subtitle.copyWith(
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.caption.copyWith(
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      trailing: DropdownButton<T>(
        value: value,
        onChanged: onChanged,
        underline: const SizedBox.shrink(),
        items: options.map((T option) {
          return DropdownMenuItem<T>(
            value: option,
            child: Text(
              option.toString(),
              style: AppTextStyles.body.copyWith(
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
  
  // Helper method to verify with PIN when disabling archive lock
  void _verifyWithPin(AppLockProvider appLockProvider) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PinScreen(
          title: 'Verify Archive PIN',
          subtitle: 'Enter your archive PIN to disable archive lock',
          onPinSubmitted: (pin) async {
            final result = await appLockProvider.authenticateArchiveWithPin(pin);
            if (result && mounted) {
              // PIN correct, proceed with disabling
              await appLockProvider.setArchiveLocked(false);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Archive lock disabled'),
                ),
              );
            } else if (mounted) {
              // PIN incorrect, show error
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Incorrect PIN. Archive lock not disabled.'),
                ),
              );
              // Return to settings without disabling
              Navigator.pop(context);
            }
          },
        ),
        fullscreenDialog: true,
      ),
    );
  }
}
