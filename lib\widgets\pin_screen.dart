import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../utils/constants.dart';

/// A modern, professionally designed PIN entry screen with responsive layout
class PinScreen extends StatefulWidget {
  /// Title shown at the top of the screen
  final String title;

  /// Subtitle or instruction text
  final String subtitle;

  /// Number of PIN digits (default: 4)
  final int pinLength;

  /// Callback when PIN is entered
  final Function(String) onPinSubmitted;

  /// Function to handle biometric authentication
  final Function()? onBiometricPressed;

  /// Whether biometric authentication is available
  final bool showBiometrics;
  
  /// Whether to hide the visibility toggle button
  final bool hideVisibilityToggle;
  
  /// Whether to obscure the PIN (can be controlled externally)
  final bool? obscurePin;
  
  /// Whether the screen is in landscape mode
  final bool isLandscape;

  const PinScreen({
    Key? key,
    required this.title,
    required this.subtitle,
    this.pinLength = 4,
    required this.onPinSubmitted,
    this.onBiometricPressed,
    this.showBiometrics = false,
    this.hideVisibilityToggle = true, // Default to hiding the visibility toggle for cleaner UI
    this.obscurePin,
    this.isLandscape = false,
  }) : super(key: key);

  @override
  State<PinScreen> createState() => _PinScreenState();
}

class _PinScreenState extends State<PinScreen> with SingleTickerProviderStateMixin {
  String _pin = '';
  late bool _obscurePin; // PIN visibility state

  // Animation controller for PIN entry feedback
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    // Initialize _obscurePin with the widget's value or default to true
    _obscurePin = widget.obscurePin ?? true;
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }
  
  @override
  void didUpdateWidget(PinScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update _obscurePin if the widget's obscurePin changes
    if (widget.obscurePin != null && widget.obscurePin != _obscurePin) {
      setState(() {
        _obscurePin = widget.obscurePin!;
      });
    }
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.grey[50],
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Calculate available space
            final availableHeight = constraints.maxHeight;
            final availableWidth = constraints.maxWidth;
            
            // Determine if we're on a very small screen
            final isVerySmallScreen = availableHeight < 500;
            
            // Calculate adaptive scaling factors
            final sizeMultiplier = math.min(1.0, availableHeight / 800);
            final sizeFactor = math.max(0.7, sizeMultiplier); // Never go below 70%
            
            // Use a different layout for landscape mode
            if (widget.isLandscape) {
              return _buildLandscapeLayout(
                availableHeight, 
                availableWidth, 
                sizeFactor, 
                isDarkMode
              );
            }
            
            // Use a more flexible layout approach with a ScrollView for small screens
            return SingleChildScrollView(
              physics: isVerySmallScreen 
                ? const AlwaysScrollableScrollPhysics() 
                : const NeverScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: availableHeight,
                ),
                child: IntrinsicHeight( 
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24 * sizeFactor),
                    child: Column(
                      children: [
                        // Top section with visibility toggle
                        SizedBox(height: 16 * sizeFactor),
                        if (!widget.hideVisibilityToggle)
                          Align(
                            alignment: Alignment.topRight,
                            child: IconButton(
                              icon: Icon(
                                _obscurePin 
                                  ? Icons.visibility_outlined 
                                  : Icons.visibility_off_outlined,
                                color: isDarkMode ? Colors.white60 : Colors.black45,
                                size: 20 * sizeFactor,
                              ),
                              splashRadius: 20 * sizeFactor,
                              onPressed: () {
                                setState(() {
                                  _obscurePin = !_obscurePin;
                                });
                              },
                            ),
                          ),
                        
                        // Lock icon in an accent-colored container
                        SizedBox(height: isVerySmallScreen ? 8 * sizeFactor : 24 * sizeFactor),
                        _buildLockIcon(sizeFactor, isDarkMode),
                        
                        // Title and subtitle
                        SizedBox(height: 20 * sizeFactor),
                        Text(
                          widget.title,
                          style: TextStyle(
                            fontSize: 28 * sizeFactor,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 8 * sizeFactor),
                        Text(
                          widget.subtitle,
                          style: TextStyle(
                            fontSize: 16 * sizeFactor,
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        // Expanded space that adapts based on screen size
                        SizedBox(height: isVerySmallScreen ? 24 * sizeFactor : 40 * sizeFactor),
                        
                        // PIN indicator dots
                        _buildPinIndicator(sizeFactor),
                        
                        // Flexible spacer that pushes keypad to the bottom on tall screens
                        Spacer(flex: isVerySmallScreen ? 1 : 2),
                        
                        // Number keypad
                        _buildKeypad(sizeFactor, isDarkMode),
                        
                        // Bottom section with biometric authentication
                        SizedBox(height: 20 * sizeFactor),
                        _buildBiometricButton(sizeFactor, isDarkMode),
                        SizedBox(height: 20 * sizeFactor),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
  
  // Build a landscape-optimized layout
  Widget _buildLandscapeLayout(
    double availableHeight, 
    double availableWidth, 
    double sizeFactor,
    bool isDarkMode
  ) {
    // For landscape, use a higher size factor to prevent tiny UI elements
    final landscapeSizeFactor = math.max(sizeFactor * 0.9, 0.7);
    
    return Container(
      width: availableWidth,
      height: availableHeight,
      color: isDarkMode ? Colors.black : Colors.grey[50],
      child: Row(
        children: [
          // Left side: Header and PIN indicators
          Expanded(
            flex: 6,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 32 * landscapeSizeFactor),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Lock icon
                  _buildLockIcon(landscapeSizeFactor * 1.3, isDarkMode),
                  
                  // Title and subtitle
                  SizedBox(height: 24 * landscapeSizeFactor),
                  Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: 28 * landscapeSizeFactor,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16 * landscapeSizeFactor),
                  Text(
                    widget.subtitle,
                    style: TextStyle(
                      fontSize: 18 * landscapeSizeFactor,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  // PIN indicator
                  SizedBox(height: 32 * landscapeSizeFactor),
                  _buildPinIndicator(landscapeSizeFactor * 1.3),
                  
                  // Biometric button
                  SizedBox(height: 32 * landscapeSizeFactor),
                  _buildBiometricButton(landscapeSizeFactor * 1.1, isDarkMode),
                ],
              ),
            ),
          ),
          
          // Vertical divider in dark mode
          if (isDarkMode)
            Container(
              height: availableHeight * 0.7,
              width: 1,
              color: Colors.grey.shade800,
            ),
          
          // Right side: Keypad
          Expanded(
            flex: 5,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16 * landscapeSizeFactor,
                vertical: 8 * landscapeSizeFactor,
              ),
              child: Center(
                child: _buildKeypad(landscapeSizeFactor * 1.1, isDarkMode, isLandscape: true),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Custom lock icon with glow effect
  Widget _buildLockIcon(double sizeFactor, bool isDarkMode) {
    return Container(
      width: 100 * sizeFactor,
      height: 100 * sizeFactor,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.accent.withOpacity(0.2),
        boxShadow: [
          BoxShadow(
            color: AppColors.accent.withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Center(
        child: Icon(
          Icons.lock_outline_rounded,
          size: 50 * sizeFactor,
          color: AppColors.accent,
        ),
      ),
    );
  }
  
  // Modern, compact biometric button
  Widget _buildBiometricButton(double sizeFactor, bool isDarkMode) {
    if (!widget.showBiometrics || widget.onBiometricPressed == null) {
      return const SizedBox.shrink();
    }
    
    return GestureDetector(
      onTap: widget.onBiometricPressed,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: 12 * sizeFactor,
          horizontal: 16 * sizeFactor,
        ),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.transparent : Colors.white,
          borderRadius: BorderRadius.circular(30 * sizeFactor),
          border: Border.all(
            color: AppColors.accent.withOpacity(0.5),
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.fingerprint,
              size: 22 * sizeFactor,
              color: AppColors.accent,
            ),
            SizedBox(width: 8 * sizeFactor),
            Text(
              'Use Biometrics',
              style: TextStyle(
                fontSize: 16 * sizeFactor,
                fontWeight: FontWeight.w500,
                color: AppColors.accent,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // PIN indicator dots
  Widget _buildPinIndicator(double sizeFactor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.pinLength,
        (index) {
          final isActive = index < _pin.length;
          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: EdgeInsets.symmetric(horizontal: 10 * sizeFactor),
            width: 18 * sizeFactor,
            height: 18 * sizeFactor,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isActive ? AppColors.accent : Colors.transparent,
              border: Border.all(
                color: isActive ? AppColors.accent : Colors.grey.shade600,
                width: 1.5,
              ),
              boxShadow: isActive ? [
                BoxShadow(
                  color: AppColors.accent.withOpacity(0.4),
                  blurRadius: 8,
                  spreadRadius: 1,
                )
              ] : null,
            ),
          );
        },
      ),
    );
  }
  
  // Keypad with responsive sizing
  Widget _buildKeypad(double sizeFactor, bool isDarkMode, {bool isLandscape = false}) {
    // Define keypad buttons for the first three rows
    final keypadButtons = [
      '1', '2', '3',
      '4', '5', '6',
      '7', '8', '9',
    ];
    
    // Calculate optimal button size based on available space
    final buttonBaseSize = isLandscape ? 75.0 : 70.0;
    final buttonSize = buttonBaseSize * sizeFactor;
    final fontSize = buttonSize * 0.45;
    final iconSize = buttonSize * 0.4;
    
    // Improved button colors for better visibility in both modes
    final buttonColor = isDarkMode ? Color(0xFF1E1E1E) : Colors.white;
    final buttonBorderColor = isDarkMode ? Colors.grey.shade800 : Colors.grey.shade400; // Darker border for light mode
    
    // Adjust spacing for landscape mode
    final horizontalSpacing = isLandscape ? 12.0 * sizeFactor : 16.0 * sizeFactor;
    final verticalSpacing = isLandscape ? 12.0 * sizeFactor : 16.0 * sizeFactor;
    
    // Use a Column with Rows instead of GridView to avoid layout issues
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Row 1: 1, 2, 3
        _buildKeypadRow(
          [keypadButtons[0], keypadButtons[1], keypadButtons[2]],
          buttonSize,
          fontSize,
          iconSize,
          buttonColor,
          buttonBorderColor,
          sizeFactor,
          horizontalSpacing,
        ),
        SizedBox(height: verticalSpacing),
        
        // Row 2: 4, 5, 6
        _buildKeypadRow(
          [keypadButtons[3], keypadButtons[4], keypadButtons[5]],
          buttonSize,
          fontSize,
          iconSize,
          buttonColor,
          buttonBorderColor,
          sizeFactor,
          horizontalSpacing,
        ),
        SizedBox(height: verticalSpacing),
        
        // Row 3: 7, 8, 9
        _buildKeypadRow(
          [keypadButtons[6], keypadButtons[7], keypadButtons[8]],
          buttonSize,
          fontSize,
          iconSize,
          buttonColor,
          buttonBorderColor,
          sizeFactor,
          horizontalSpacing,
        ),
        SizedBox(height: verticalSpacing),
        
        // Row 4: 0 and backspace - center the 0 button
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty space on the left to balance layout and center '0'
            SizedBox(width: buttonSize / 2),
            
            // Center the 0 button
            _buildKeypadButton(
              child: Text(
                '0',
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).brightness == Brightness.dark 
                      ? Colors.white 
                      : Colors.black87, // Use dark text on light background for better contrast
                ),
              ),
              onTap: () => _handleDigitPress('0'),
              size: buttonSize,
              color: buttonColor,
              borderColor: buttonBorderColor,
              horizontalMargin: horizontalSpacing,
            ),
            
            // Backspace button
            _buildKeypadButton(
              child: Icon(
                Icons.backspace_outlined,
                size: iconSize,
                color: AppColors.accent,
              ),
              onTap: _handleBackspace,
              size: buttonSize,
              color: buttonColor,
              borderColor: buttonBorderColor,
              horizontalMargin: horizontalSpacing,
            ),
            
            // Empty space on the right to balance layout
            SizedBox(width: buttonSize / 2),
          ],
        ),
      ],
    );
  }
  
  // Helper method to build a row of keypad buttons
  Widget _buildKeypadRow(
    List<String> keys,
    double buttonSize,
    double fontSize,
    double iconSize,
    Color buttonColor,
    Color borderColor,
    double sizeFactor,
    double horizontalSpacing,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: keys.map((key) {
        // Empty button placeholder
        if (key.isEmpty) {
          return SizedBox(width: buttonSize, height: buttonSize);
        }
        
        // Backspace button
        if (key == 'backspace') {
          return _buildKeypadButton(
            child: Icon(
              Icons.backspace_outlined,
              size: iconSize,
              color: Theme.of(context).brightness == Brightness.dark
                ? AppColors.accent
                : AppColors.accent.withOpacity(0.8), // Slightly adjusted for light mode
            ),
            onTap: _handleBackspace,
            size: buttonSize,
            color: buttonColor,
            borderColor: borderColor,
            horizontalMargin: horizontalSpacing / 2,
          );
        }
        
        // Number button
        return _buildKeypadButton(
          child: Text(
            key,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.white 
                  : Colors.black87, // Use dark text on light background for better contrast
            ),
          ),
          onTap: () => _handleDigitPress(key),
          size: buttonSize,
          color: buttonColor,
          borderColor: borderColor,
          horizontalMargin: horizontalSpacing / 2,
        );
      }).toList(),
    );
  }
  
  // Helper method to build consistent keypad buttons
  Widget _buildKeypadButton({
    required Widget child,
    required VoidCallback onTap,
    required double size,
    required Color color,
    required Color borderColor,
    double horizontalMargin = 8.0,
  }) {
    return GestureDetector(
      onTap: () {
        // Add haptic feedback
        HapticFeedback.lightImpact();
        
        // Animate button press
        _animationController.forward().then((_) {
          _animationController.reverse();
        });
        
        // Call the provided onTap callback
        onTap();
      },
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          width: size,
          height: size,
          margin: EdgeInsets.symmetric(horizontal: horizontalMargin),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
            border: Border.all(color: borderColor, width: 1.5), // Thicker border for better visibility
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3), // Darker shadow
                blurRadius: 8,   // Larger blur radius
                spreadRadius: 1, // Add spread for more visible shadow
                offset: const Offset(0, 3),  // Slightly larger offset
              ),
            ],
          ),
          child: Center(child: child),
        ),
      ),
    );
  }
  
  // Handle digit press
  void _handleDigitPress(String digit) {
    // Only add digit if PIN length is less than the required length
    if (_pin.length < widget.pinLength) {
      setState(() {
        _pin += digit;
      });
      
      // If PIN is complete, submit it
      if (_pin.length == widget.pinLength) {
        widget.onPinSubmitted(_pin);
      }
    }
  }
  
  // Handle backspace press
  void _handleBackspace() {
    // Only remove digit if PIN is not empty
    if (_pin.isNotEmpty) {
      setState(() {
        _pin = _pin.substring(0, _pin.length - 1);
      });
    }
  }
  
  // Clear PIN
  void _clearPin() {
    setState(() {
      _pin = '';
    });
  }
}
