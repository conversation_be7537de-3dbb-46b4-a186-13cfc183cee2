import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// App Colors
class AppColors {
  static const Color primary = Color(0xFF2C3E50);
  static const Color secondary = Color(0xFF34495E);
  static const Color accent = Color(0xFF1ABC9C);
  static const Color background = Color(0xFF121212);
  static const Color cardBackground = Color(0xFF1E1E1E);
  static const Color textPrimary = Colors.white;
  static const Color textSecondary = Color(0xFFB3B3B3);
  static const Color divider = Color(0xFF3D3D3D);

  // Note theme colors - updated from image
  static const List<Color> noteColors = [
    Color(0xFF9c27b0), // Purple
    Color(0xFFad1457), // Magenta
    Color(0xFFd32f2f), // Brick Red
    Color(0xFF7e3704), // Brown
    Color(0xFFe78800), // Gold
    Color(0xFF1b5e20), // Green
    Color(0xFF00796b), // Teal
    Color(0xFF0277bd), // Blue
    Color(0xFF283593), // Navy
    Color(0xFF6a1b9a), // Darker Purple
    Color(0xFF8e3e69), // Pink
    Color(0xFF424242), // Dark Gray
  ];
}

// Text Styles
class AppTextStyles {
  static final TextStyle heading1 = GoogleFonts.poppins(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static final TextStyle heading2 = GoogleFonts.poppins(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static final TextStyle body = GoogleFonts.poppins(
    fontSize: 16,
    color: AppColors.textPrimary,
  );

  static final TextStyle caption = GoogleFonts.poppins(
    fontSize: 14,
    color: AppColors.textSecondary,
  );
  
  static final TextStyle button = GoogleFonts.poppins(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.accent,
  );
  
  static final TextStyle subtitle = GoogleFonts.poppins(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
  );
}

// App Theme
class AppTheme {
  static ThemeData darkTheme = ThemeData.dark().copyWith(
    primaryColor: AppColors.primary,
    scaffoldBackgroundColor: AppColors.background,
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.primary,
      elevation: 0,
    ),
    cardTheme: const CardTheme(
      color: AppColors.cardBackground,
      elevation: 4,
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColors.accent,
      foregroundColor: Colors.white,
    ),
    dividerColor: AppColors.divider,
    colorScheme: ColorScheme.fromSwatch().copyWith(
      primary: AppColors.primary,
      secondary: AppColors.accent,
      background: AppColors.background,
    ),
  );
}

// Font Options
class FontOptions {
  static const List<String> fontFamilies = [
    'Poppins',
    'Roboto',
    'Lato',
    'OpenSans',
    'Montserrat',
    'SourceSansPro',
    'Raleway',
  ];
}
