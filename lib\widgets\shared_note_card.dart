import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:convert';
import '../models/note.dart';
import '../models/label.dart';
import '../providers/labels_provider.dart';
import '../screens/note_detail_screen.dart';
import '../utils/date_formatter.dart';

class SharedNoteCard extends StatefulWidget {
  final Note note;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final bool isInSelectionMode;
  final VoidCallback? onTap;
  final bool showPinIcon;
  final VoidCallback? onPinTap;
  final List<Widget> actionButtons;

  const SharedNoteCard({
    Key? key,
    required this.note,
    this.onLongPress,
    this.isSelected = false,
    this.isInSelectionMode = false,
    this.onTap,
    this.showPinIcon = true,
    this.onPinTap,
    this.actionButtons = const [],
  }) : super(key: key);

  @override
  State<SharedNoteCard> createState() => _SharedNoteCardState();
}

class _SharedNoteCardState extends State<SharedNoteCard> with SingleTickerProviderStateMixin {
  List<Label> _noteLabels = [];
  bool _isLoadingLabels = true;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  String _contentPreview = '';
  List<String> _imageUrls = [];

  @override
  void initState() {
    super.initState();
    _loadLabelsForNote();
    _parseContentPreview();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    
    // Create scale animation for card press effect
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _parseContentPreview() {
    try {
      if (widget.note.content.isEmpty) {
        _contentPreview = '';
        _imageUrls = [];
        return;
      }

      // Try to parse the Delta JSON
      final dynamic delta = jsonDecode(widget.note.content);
      
      if (delta is List && delta.isNotEmpty) {
        // Process Delta operations
        StringBuffer plainText = StringBuffer();
        int charCount = 0;
        bool hasEnoughTextForPreview = false;
        _imageUrls = [];
        
        // First pass: scan the entire note to collect all images regardless of position
        for (var op in delta) {
          if (op is Map && op.containsKey('insert')) {
            final insert = op['insert'];
            
            if (insert is Map && insert.containsKey('image')) {
              // This is an image embed
              final imageUrl = insert['image'];
              if (imageUrl is String && imageUrl.isNotEmpty) {
                _imageUrls.add(imageUrl);
              }
            }
          }
        }
        
        // Second pass: extract text for preview (limited amount)
        for (var op in delta) {
          if (hasEnoughTextForPreview) break;
          
          if (op is Map && op.containsKey('insert')) {
            final insert = op['insert'];
            
            if (insert is String) {
              plainText.write(insert);
              charCount += insert.length;
              
              // Check if we have enough text for preview
              if (charCount > 150) {
                hasEnoughTextForPreview = true;
              }
            }
          }
        }
        
        String text = plainText.toString();
        
        // Smart truncation - try to break at sentence or paragraph
        if (text.length > 100) {
          // First try to find a paragraph break
          int paraBreak = text.indexOf('\n', 80);
          if (paraBreak > 0 && paraBreak < 150) {
            text = text.substring(0, paraBreak);
          } else {
            // Try to find a sentence break
            int sentenceBreak = -1;
            for (var endChar in ['. ', '! ', '? ']) {
              int idx = text.indexOf(endChar, 80);
              if (idx > 0 && idx < 150 && (sentenceBreak == -1 || idx < sentenceBreak)) {
                sentenceBreak = idx + 1; // Include the punctuation
              }
            }
            
            if (sentenceBreak > 0) {
              text = text.substring(0, sentenceBreak);
            } else {
              // If no sentence break, just truncate with ellipsis
              text = text.substring(0, 100) + '...';
            }
          }
        }

        // Clean up whitespace
        text = text.replaceAll(RegExp(r'\n{2,}'), '\n');
        text = text.trim();
        
        _contentPreview = text;
      } else {
        // Fallback for non-delta content
        _contentPreview = widget.note.content.length > 100 
            ? '${widget.note.content.substring(0, 100)}...'
            : widget.note.content;
      }
    } catch (e) {
      // If parsing fails, use a simple approach
      _contentPreview = widget.note.content.length > 100 
          ? '${widget.note.content.substring(0, 100)}...'
          : widget.note.content;
    }
  }

  Future<void> _loadLabelsForNote() async {
    if (widget.note.id != null) {
      setState(() {
        _isLoadingLabels = true;
      });

      try {
        final labelsProvider = Provider.of<LabelsProvider>(context, listen: false);
        final labels = await labelsProvider.getLabelsForNote(widget.note.id!);
        
        if (mounted) {
          setState(() {
            _noteLabels = labels;
            _isLoadingLabels = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingLabels = false;
          });
        }
      }
    } else {
      setState(() {
        _isLoadingLabels = false;
      });
    }
  }

  Widget _buildImagePreview() {
    if (_imageUrls.isEmpty) return const SizedBox.shrink();
    
    // Adaptive height calculation based on screen size
    final screenWidth = MediaQuery.of(context).size.width;
    
    // Scale max height based on screen width (more responsive)
    final double maxHeight = _imageUrls.length == 1 
        ? (screenWidth < 360 ? 120.0 : 150.0)
        : (screenWidth < 360 ? 100.0 : 120.0);
    
    if (_imageUrls.length == 1) {
      // Single image layout - with improved constraints
      return ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Use available width to properly constrain the image
            final availableWidth = constraints.maxWidth > 0 ? constraints.maxWidth : screenWidth - 16;
            
            return Container(
              width: double.infinity,
              constraints: BoxConstraints(
                maxHeight: maxHeight,
                maxWidth: availableWidth,
              ),
              child: AspectRatio(
                aspectRatio: 16/9, // Fixed aspect ratio prevents layout jumps
                child: Image.network(
                  _imageUrls.first,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    color: Colors.grey[200],
                    child: const Center(child: Icon(Icons.broken_image, color: Colors.grey)),
                  ),
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      color: Colors.grey[100],
                      child: const Center(child: CircularProgressIndicator()),
                    );
                  },
                ),
              ),
            );
          },
        ),
      );
    } else {
      // Multiple images grid layout - improved to prevent overflow
      int crossAxisCount = _imageUrls.length == 2 ? 2 : (_imageUrls.length <= 4 ? 2 : 3);
      double aspectRatio = _imageUrls.length == 2 ? 1.5 : 1.0;
      
      return ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Determine available width for proper layout
            final availableWidth = constraints.maxWidth > 0 ? constraints.maxWidth : screenWidth - 16;
            
            // Calculate the maximum height that won't overflow
            final calculatedHeight = (availableWidth / crossAxisCount) / aspectRatio;
            final safeHeight = calculatedHeight > maxHeight ? maxHeight : calculatedHeight;
            
            return Container(
              constraints: BoxConstraints(maxHeight: safeHeight),
              width: double.infinity,
              child: GridView.count(
                padding: EdgeInsets.zero,
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: 2,
                crossAxisSpacing: 2,
                childAspectRatio: aspectRatio,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: _imageUrls.take(crossAxisCount * 2).map((url) => _buildImageThumbnail(url)).toList()
                  // Add 'more' indicator if needed
                  + (_imageUrls.length > crossAxisCount * 2 ? [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black45,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Center(
                        child: Text(
                          '+${_imageUrls.length - (crossAxisCount * 2 - 1)}',
                          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                    )
                  ] : []),
              ),
            );
          },
        ),
      );
    }
  }
  
  Widget _buildImageThumbnail(String url) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4),
      child: AspectRatio(
        aspectRatio: 1.0, // Square aspect ratio for consistent thumbnails
        child: Image.network(
          url,
          fit: BoxFit.cover,
          frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
            return child; // Simple frame builder for performance
          },
          errorBuilder: (context, error, stackTrace) => Container(
            color: Colors.grey[200],
            child: const Center(child: Icon(Icons.broken_image, color: Colors.grey, size: 20)),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    // Determine card background color
    Color cardColor;
    if (widget.note.themeColor != null) {
      // Convert the stored color value string back to a Color
      final colorValue = int.parse(widget.note.themeColor!);
      final baseColor = Color(colorValue);
      // Apply the same opacity level as the note detail screen for consistency
      cardColor = isDarkMode 
        ? baseColor.withOpacity(0.2) // Darker mode - more transparent
        : baseColor.withOpacity(0.1); // Light mode - very subtle
    } else {
      // Default card color when no theme is selected
      cardColor = isDarkMode
          ? const Color(0xFF2D2D2D)
          : Colors.white;
    }
    
    // Add a slight border for pinned notes or selected notes
    BorderSide border;
    if (widget.isSelected) {
      // Selection border is stronger
      border = BorderSide(
        color: isDarkMode ? Colors.blue.shade400 : Colors.blue.shade700,
        width: 2.0,
      );
    } else if (widget.note.isPinned) {
      border = BorderSide(
        color: isDarkMode ? Colors.white30 : Colors.black12,
        width: 1.5,
      );
    } else {
      border = BorderSide.none;
    }
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: GestureDetector(
        onTapDown: (_) => _animationController.forward(),
        onTapUp: (_) => _animationController.reverse(),
        onTapCancel: () => _animationController.reverse(),
        onLongPress: widget.onLongPress,
        onTap: () {
          _animationController.reverse();
          if (widget.onTap != null) {
            widget.onTap!();
          } else if (widget.note.id != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => NoteDetailScreen(noteId: widget.note.id),
              ),
            );
          }
        },
        child: Stack(
          children: [
            // The note card with balanced spacing
            Card(
              elevation: widget.isSelected ? 2 : (widget.note.isPinned ? 1 : 1),
              margin: const EdgeInsets.all(2.0), // Restore small margin for better appearance
              clipBehavior: Clip.antiAlias, // Ensure content is clipped properly
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8), // Keep the smaller radius
                side: border,
              ),
              color: cardColor,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min, // Use minimum space needed
                children: [
                  // Image preview if available - wrapped in overflow protection
                  if (_imageUrls.isNotEmpty)
                    LayoutBuilder(
                      builder: (context, constraints) {
                        return SizedBox(
                          width: constraints.maxWidth,
                          child: _buildImagePreview(),
                        );
                      },
                    ),
                  
                  // Note content with balanced padding
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8.0), // Balanced padding
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min, // Use minimum space needed
                          children: [
                            // Title row with pin icon
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Title (expanded to take available space)
                                Expanded(
                                  child: widget.note.title.isNotEmpty
                                      ? Text(
                                          widget.note.title,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                          maxLines: 1, // Reduce to 1 line
                                          overflow: TextOverflow.ellipsis,
                                        )
                                      : const SizedBox.shrink(),
                                ),
                                
                                // Pin icon (only show if note is pinned and not in selection mode)
                                if (!widget.isInSelectionMode && widget.showPinIcon && widget.note.isPinned)
                                  // Pin icon (indicator only, not interactive)
                                  Padding(
                                    padding: const EdgeInsets.only(left: 4.0),
                                    child: Icon(
                                      Icons.push_pin,
                                      size: 20,
                                      color: isDarkMode ? Colors.amber : Colors.orange.shade700,
                                    ),
                                  ),
                              ],
                            ),
                            
                            // Only show spacing if title exists
                            if (widget.note.title.isNotEmpty)
                              const SizedBox(height: 2),
                            
                            // Content preview
                            if (_contentPreview.isNotEmpty)
                              LimitedBox(
                                maxHeight: constraints.maxHeight * 0.5, // Reduce to 50% of available height
                                child: Text(
                                  _contentPreview,
                                  style: TextStyle(
                                    fontSize: 13, // Slightly smaller font
                                    color: isDarkMode ? Colors.white70 : Colors.black87,
                                  ),
                                  maxLines: 4, // Reduce max lines to prevent overflow
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            
                            const SizedBox(height: 2),
                            
                            // Labels
                            if (_noteLabels.isNotEmpty && !_isLoadingLabels)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 2.0),
                                child: Wrap(
                                  spacing: 2,
                                  runSpacing: 2,
                                  children: _noteLabels.map((label) => Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
                                    decoration: BoxDecoration(
                                      color: label.color.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: label.color.withOpacity(0.5),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      label.name,
                                      style: TextStyle(
                                        fontSize: 10, // Smaller font
                                        color: isDarkMode ? Colors.white70 : Colors.black87,
                                      ),
                                    ),
                                  )).toList(),
                                ),
                              ),
                            
                            // Footer with date and action buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                // Date - constrained to available width
                                Expanded(
                                  child: Text(
                                    DateFormatter.formatDate(widget.note.updatedAt),
                                    style: TextStyle(
                                      fontSize: 11, // Smaller font
                                      color: isDarkMode ? Colors.white54 : Colors.black54,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                
                                // Action buttons (if provided and not in selection mode)
                                if (!widget.isInSelectionMode && widget.actionButtons.isNotEmpty)
                                  Flexible(
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: widget.actionButtons.map((button) {
                                        // Make buttons smaller to prevent overflow
                                        return SizedBox(
                                          height: 28, // Smaller height
                                          width: 28, // Smaller width
                                          child: button,
                                        );
                                      }).toList(),
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        );
                      }
                    ),
                  ),
                ],
              ),
            ),
            
            // Selection indicator
            if (widget.isInSelectionMode)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: widget.isSelected
                        ? isDarkMode ? Colors.blue.shade700 : Colors.blue
                        : isDarkMode ? Colors.grey.shade800.withOpacity(0.7) : Colors.grey.shade200.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isDarkMode ? Colors.white70 : Colors.black38,
                      width: 1,
                    ),
                  ),
                  child: widget.isSelected
                      ? const Icon(
                          Icons.check,
                          size: 16,
                          color: Colors.white,
                        )
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
} 