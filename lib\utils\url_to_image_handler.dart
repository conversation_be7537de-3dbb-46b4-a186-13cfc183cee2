import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'dart:async';

/// A handler to detect image URLs in the editor and convert them to embedded images
class UrlToImageHandler {
  static const List<String> _imageExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'
  ];
  
  /// Check if a URL points to an image based on extension or image hosting patterns
  static bool isImageUrl(String url) {
    final String lowerCaseUrl = url.toLowerCase();
    
    // Check common image extensions
    for (final ext in _imageExtensions) {
      if (lowerCaseUrl.endsWith(ext)) {
        return true;
      }
    }
    
    // Check known image hosting patterns
    if (lowerCaseUrl.contains('imgur.com/') ||
        lowerCaseUrl.contains('i.imgur.com/') ||
        lowerCaseUrl.contains('.googleusercontent.com/') ||
        lowerCaseUrl.contains('images.unsplash.com/') ||
        lowerCaseUrl.contains('media.giphy.com/') ||
        (lowerCaseUrl.contains('pbs.twimg.com/') && lowerCaseUrl.contains('/media/')) ||
        (lowerCaseUrl.contains('cloudinary.com/') && lowerCaseUrl.contains('/image/')) ||
        (lowerCaseUrl.contains('res.cloudinary.com'))) {
      return true;
    }
    
    return false;
  }
  
  /// Attach a listener to a QuillController to detect and convert image URLs
  static void attachToController(QuillController controller) {
    // Current timeout to prevent multiple rapid conversions
    Timer? _debounceTimer;
    
    // Track last processed content to avoid redundant processing
    String _lastProcessedContent = '';
    
    // Listen to document changes
    controller.document.changes.listen((_) {
      if (_debounceTimer?.isActive ?? false) {
        return;
      }
      
      // Debounce to avoid multiple conversions during rapid typing
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        final currentContent = controller.document.toPlainText();
        
        // Only process if content has changed significantly (more than just cursor movement)
        // But always process after a paste operation
        if (currentContent != _lastProcessedContent) {
          _processDocumentForImageUrls(controller);
          _lastProcessedContent = controller.document.toPlainText();
        }
      });
    });
    
    // Also listen to clipboard paste events
    ServicesBinding.instance.keyboard.addHandler((KeyEvent event) {
      // Check for paste command (Ctrl+V or Cmd+V)
      if (event is KeyDownEvent) {
        if ((event.logicalKey == LogicalKeyboardKey.keyV) && 
            (HardwareKeyboard.instance.isControlPressed || 
             HardwareKeyboard.instance.isMetaPressed)) {
          // Schedule a check after paste is completed
          Future.delayed(const Duration(milliseconds: 100), () {
            _processDocumentForImageUrls(controller);
            // Update last processed content after paste operation
            _lastProcessedContent = controller.document.toPlainText();
            
            // Schedule an additional check for multiple image URLs
            // This helps when multiple URLs are pasted at once
            Future.delayed(const Duration(milliseconds: 300), () {
              _processDocumentForImageUrls(controller);
              _lastProcessedContent = controller.document.toPlainText();
            });
          });
          return false; // Don't consume the event
        }
      }
      return false;
    });
  }
  
  /// Process the document to find and convert image URLs
  static void _processDocumentForImageUrls(QuillController controller) {
    final currentContent = controller.document.toPlainText();
    
    // Check for URLs in the text
    final RegExp urlRegex = RegExp(
      r'https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)',
      caseSensitive: false,
      multiLine: true,
    );
    
    final matches = urlRegex.allMatches(currentContent);
    
    // Skip processing if no URLs found
    if (matches.isEmpty) {
      return;
    }
    
    // Process matches from end to start to avoid offset issues
    final List<MapEntry<int, String>> urlsToProcess = [];
    
    // First collect all image URLs with their positions
    for (final match in matches) {
      final url = match.group(0)!;
      if (isImageUrl(url)) {
        // Make sure the URL is still present in the document (not already processed)
        final currentText = controller.document.toPlainText();
        final actualPosition = currentText.indexOf(url);
        
        if (actualPosition >= 0) {
          // Use the actual current position of the URL
          urlsToProcess.add(MapEntry(actualPosition, url));
        }
      }
    }
    
    // Skip if no image URLs to process
    if (urlsToProcess.isEmpty) {
      return;
    }
    
    // Sort in reverse order to process from end to beginning
    urlsToProcess.sort((a, b) => b.key.compareTo(a.key));
    
    // Now process the URLs one by one with small delays
    // to allow the document to stabilize between changes
    _processNextUrl(controller, urlsToProcess, 0);
  }
  
  /// Process URLs one by one with small delays between them
  static void _processNextUrl(QuillController controller, List<MapEntry<int, String>> urls, int index) {
    if (index >= urls.length) return;
    
    final entry = urls[index];
    final start = entry.key;
    final url = entry.value;
    
    // Verify the URL is still at the expected position
    final currentText = controller.document.toPlainText();
    if (start < currentText.length && currentText.substring(start, start + url.length) == url) {
      // Replace the URL with an embedded image
      controller.replaceText(
        start, 
        url.length, 
        '', 
        null,
      );
      
      // Insert the image at the same position
      controller.updateSelection(
        TextSelection.collapsed(offset: start),
        ChangeSource.local,
      );
      
      // Use custom insert image
      _insertImage(controller, url);
      
      // Process the next URL after a small delay
      if (index + 1 < urls.length) {
        Future.delayed(const Duration(milliseconds: 50), () {
          _processNextUrl(controller, urls, index + 1);
        });
      }
    } else {
      // URL no longer at expected position, skip to next
      if (index + 1 < urls.length) {
        _processNextUrl(controller, urls, index + 1);
      }
    }
  }
  
  /// Insert an image into the document at the current selection
  static void _insertImage(QuillController controller, String url) {
    final index = controller.selection.baseOffset;
    
    // Create embed
    final embed = BlockEmbed.image(url);
    
    // Insert image embed
    controller.document.insert(index, embed);
    
    // Move cursor after image
    controller.updateSelection(
      TextSelection.collapsed(offset: index + 1),
      ChangeSource.local,
    );
  }
}
