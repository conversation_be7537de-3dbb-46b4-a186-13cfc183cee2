import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/note.dart';
import '../models/label.dart';
import '../providers/labels_provider.dart';
import '../screens/note_detail_screen.dart';

import '../utils/date_formatter.dart';
import './note_labels_view.dart';
import 'dart:ui';
import 'dart:convert';

class NoteItem extends StatefulWidget {
  final Note note;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final bool isInSelectionMode;
  final VoidCallback? onTap;

  const NoteItem({
    Key? key,
    required this.note,
    this.onLongPress,
    this.isSelected = false,
    this.isInSelectionMode = false,
    this.onTap,
  }) : super(key: key);

  @override
  State<NoteItem> createState() => _NoteItemState();
}

class _NoteItemState extends State<NoteItem> with SingleTickerProviderStateMixin {
  List<Label> _noteLabels = [];
  bool _isLoadingLabels = true;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  String _contentPreview = '';
  bool _hasAdditionalContent = false;
  List<String> _imageUrls = [];

  @override
  void initState() {
    super.initState();
    _loadLabelsForNote();
    _parseContentPreview();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    
    // Create scale animation for card press effect
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _parseContentPreview() {
    try {
      if (widget.note.content.isEmpty) {
        _contentPreview = '';
        _imageUrls = [];
        return;
      }

      // Try to parse the Delta JSON
      final dynamic delta = jsonDecode(widget.note.content);
      
      if (delta is List && delta.isNotEmpty) {
        // Process Delta operations
        StringBuffer plainText = StringBuffer();
        int charCount = 0;
        _imageUrls = [];
        
        for (var op in delta) {
          if (op is Map && op.containsKey('insert')) {
            final insert = op['insert'];
            
            if (insert is String) {
              plainText.write(insert);
              charCount += insert.length;
              
              // Check if this operation has attributes (formatting)
              // Rich text detection was removed as it's not needed
            } else if (insert is Map && insert.containsKey('image')) {
              // This is an image embed
              final imageUrl = insert['image'];
              if (imageUrl is String && imageUrl.isNotEmpty) {
                _imageUrls.add(imageUrl);
              }
            }
          }
          
          // Stop if we've collected enough text for preview
          if (charCount > 150) {
            _hasAdditionalContent = true;
            break;
          }
        }
        
        String text = plainText.toString();
        
        // Smart truncation - try to break at sentence or paragraph
        if (text.length > 100) {
          // First try to find a paragraph break
          int paraBreak = text.indexOf('\n', 80);
          if (paraBreak > 0 && paraBreak < 150) {
            text = text.substring(0, paraBreak);
          } else {
            // Try to find a sentence break
            int sentenceBreak = -1;
            for (var endChar in ['. ', '! ', '? ']) {
              int idx = text.indexOf(endChar, 80);
              if (idx > 0 && idx < 150 && (sentenceBreak == -1 || idx < sentenceBreak)) {
                sentenceBreak = idx + 1; // Include the punctuation
              }
            }
            
            if (sentenceBreak > 0) {
              text = text.substring(0, sentenceBreak);
            } else {
              // If no sentence break, just truncate with ellipsis
              text = text.substring(0, 100) + '...';
            }
          }
          
          _hasAdditionalContent = true;
        }

        // Clean up whitespace
        text = text.replaceAll(RegExp(r'\n{2,}'), '\n');
        text = text.trim();
        
        _contentPreview = text;
        _hasAdditionalContent = _hasAdditionalContent || _imageUrls.isNotEmpty;
      } else {
        // Fallback for non-delta content
        _contentPreview = widget.note.content.length > 100 
            ? '${widget.note.content.substring(0, 100)}...'
            : widget.note.content;
      }
    } catch (e) {
      // If parsing fails, use a simple approach
      _contentPreview = widget.note.content.length > 100 
          ? '${widget.note.content.substring(0, 100)}...'
          : widget.note.content;
    }
  }

  Future<void> _loadLabelsForNote() async {
    if (widget.note.id != null) {
      setState(() {
        _isLoadingLabels = true;
      });

      try {
        final labelsProvider = Provider.of<LabelsProvider>(context, listen: false);
        final labels = await labelsProvider.getLabelsForNote(widget.note.id!);
        
        if (mounted) {
          setState(() {
            _noteLabels = labels;
            _isLoadingLabels = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingLabels = false;
          });
        }
      }
    } else {
      setState(() {
        _isLoadingLabels = false;
      });
    }
  }

  Widget _buildImagePreview() {
    if (_imageUrls.isEmpty) return const SizedBox.shrink();
    
    // Constrain the height based on number of images
    final double maxHeight = _imageUrls.length == 1 ? 150.0 : 120.0;
    
    if (_imageUrls.length == 1) {
      // Single image layout - use ClipRRect with top corners only
      return ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: maxHeight,
          ),
          child: Image.network(
            _imageUrls.first,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              height: 100,
              color: Colors.grey[200],
              child: const Icon(Icons.broken_image, color: Colors.grey),
            ),
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                height: 100,
                color: Colors.grey[100],
                child: const Center(child: CircularProgressIndicator()),
              );
            },
          ),
        ),
      );
    } else {
      // Multiple images grid layout - ensure proper clipping
      int crossAxisCount = _imageUrls.length == 2 ? 2 : 3;
      double aspectRatio = _imageUrls.length == 2 ? 1.5 : 1.0;
      
      return ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        child: Container(
          constraints: BoxConstraints(maxHeight: maxHeight),
          width: double.infinity,
          child: GridView.count(
            padding: EdgeInsets.zero, // Ensure no internal padding
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: 4,
            crossAxisSpacing: 4,
            childAspectRatio: aspectRatio,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: _imageUrls.take(4).map((url) => _buildImageThumbnail(url)).toList()
              // Add 'more' indicator if needed
              + (_imageUrls.length > 4 ? [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black45,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(
                    child: Text(
                      '+${_imageUrls.length - 3}',
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                )
              ] : []),
          ),
        ),
      );
    }
  }
  
  Widget _buildImageThumbnail(String url) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4),
      child: Image.network(
        url,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: Colors.grey[200],
          child: const Icon(Icons.broken_image, color: Colors.grey),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    // Determine card background color with subtle opacity like in note detail screen
    Color cardColor;
    if (widget.note.themeColor != null) {
      // Convert the stored color value string back to a Color with subtle opacity
      final colorValue = int.parse(widget.note.themeColor!);
      final baseColor = Color(colorValue);
      // Apply the same opacity level as the note detail screen for consistency
      cardColor = isDarkMode 
        ? baseColor.withOpacity(0.2) // Darker mode - more transparent
        : baseColor.withOpacity(0.1); // Light mode - very subtle
    } else {
      // Default card color when no theme is selected
      cardColor = isDarkMode
          ? const Color(0xFF2D2D2D)
          : Colors.white;
    }
    
    // Add a slight border for pinned notes or selected notes
    BorderSide border;
    if (widget.isSelected) {
      // Selection border is stronger
      border = BorderSide(
        color: isDarkMode ? Colors.blue.shade400 : Colors.blue.shade700,
        width: 2.0,
      );
    } else if (widget.note.isPinned) {
      border = BorderSide(
        color: isDarkMode ? Colors.white30 : Colors.black12,
        width: 1.5,
      );
    } else {
      border = BorderSide.none;
    }
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: GestureDetector(
        onTapDown: (_) => _animationController.forward(),
        onTapUp: (_) => _animationController.reverse(),
        onTapCancel: () => _animationController.reverse(),
        onLongPress: widget.onLongPress,
        onTap: () {
          _animationController.reverse();
          if (widget.onTap != null) {
            widget.onTap!();
          } else if (widget.note.id != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => NoteDetailScreen(noteId: widget.note.id),
              ),
            );
          }
        },
        child: Stack(
          children: [
            // The note card
            Card(
              elevation: widget.isSelected ? 4 : (widget.note.isPinned ? 3 : 1),
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: border,
              ),
              color: cardColor,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image preview if available
                  if (_imageUrls.isNotEmpty) _buildImagePreview(),
                  
                  // Note content
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title row with pin icon
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Title (expanded to take available space)
                            Expanded(
                              child: widget.note.title.isNotEmpty
                                  ? Text(
                                      widget.note.title,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    )
                                  : const SizedBox.shrink(),
                            ),
                            
                            // Pin icon (only show if note is pinned and not in selection mode)
                            if (!widget.isInSelectionMode && widget.note.isPinned)
                              // Pin icon (indicator only, not interactive)
                              Padding(
                                padding: const EdgeInsets.only(left: 4.0),
                                child: Icon(
                                  Icons.push_pin,
                                  size: 20,
                                  color: isDarkMode ? Colors.amber : Colors.orange.shade700,
                                ),
                              ),
                          ],
                        ),
                        
                        // Only show spacing if title exists
                        if (widget.note.title.isNotEmpty)
                          const SizedBox(height: 8),
                        
                        // Content preview
                        if (_contentPreview.isNotEmpty)
                          Text(
                            _contentPreview,
                            style: TextStyle(
                              fontSize: 14,
                              color: isDarkMode ? Colors.white70 : Colors.black87,
                            ),
                            maxLines: 8,
                            overflow: TextOverflow.ellipsis,
                          ),
                        
                        const SizedBox(height: 8),
                        
                        // Labels
                        if (_noteLabels.isNotEmpty && !_isLoadingLabels)
                          NoteLabelsView(labels: _noteLabels),
                        
                        const SizedBox(height: 4),
                        
                        // Footer with date and reminder indicator
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Date
                            Text(
                              DateFormatter.formatDate(widget.note.updatedAt),
                              style: TextStyle(
                                fontSize: 12,
                                color: isDarkMode ? Colors.white54 : Colors.black54,
                              ),
                            ),
                            
                            // Reminder indicator
                            if (widget.note.hasReminder && widget.note.reminderTime != null)
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: isDarkMode ? Colors.white54 : Colors.black54,
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Selection indicator
            if (widget.isInSelectionMode)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: widget.isSelected
                        ? isDarkMode ? Colors.blue.shade700 : Colors.blue
                        : isDarkMode ? Colors.grey.shade800.withOpacity(0.7) : Colors.grey.shade200.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isDarkMode ? Colors.white70 : Colors.black38,
                      width: 1,
                    ),
                  ),
                  child: widget.isSelected
                      ? const Icon(
                          Icons.check,
                          size: 16,
                          color: Colors.white,
                        )
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
} 