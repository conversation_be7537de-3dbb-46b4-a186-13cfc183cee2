import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/note.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';
import '../services/memory_service.dart';

class NotesProvider with ChangeNotifier {
  final DatabaseService _databaseService;
  final NotificationService _notificationService;
  final MemoryService _memoryService;
  
  List<Note> _notes = [];
  List<Note> _pinnedNotes = [];
  List<Note> _archivedNotes = [];
  List<Note> _trashedNotes = [];
  bool _isLoading = false;

  // Timer for auto-checking trash expiration
  Timer? _trashExpirationTimer;

  List<Note> get notes => _notes;
  List<Note> get pinnedNotes => _pinnedNotes;
  List<Note> get archivedNotes => _archivedNotes;
  List<Note> get trashedNotes => _trashedNotes;
  bool get isLoading => _isLoading;

  NotesProvider({
    DatabaseService? databaseService,
    NotificationService? notificationService,
    MemoryService? memoryService,
  }) : 
    _databaseService = databaseService ?? DatabaseService(),
    _notificationService = notificationService ?? NotificationService(),
    _memoryService = memoryService ?? MemoryService() {
    fetchNotes();

    // Setup timer to check for trash expiration every day
    _trashExpirationTimer = Timer.periodic(
      const Duration(days: 1),
      (_) => _deleteExpiredTrash(),
    );
  }

  @override
  void dispose() {
    _trashExpirationTimer?.cancel();
    super.dispose();
  }

  Future<void> fetchNotes() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Fetch active notes (not deleted)
      final allNotes = await _databaseService.getNotes();
      
      // Separate notes into pinned and unpinned
      _pinnedNotes = allNotes.where((note) => 
        note.isPinned && !note.isArchived && !note.isDeleted).toList();
      _notes = allNotes.where((note) => 
        !note.isPinned && !note.isArchived && !note.isDeleted).toList();
      
      _archivedNotes = allNotes.where((note) => note.isArchived && !note.isDeleted).toList();
      _trashedNotes = allNotes.where((note) => note.isDeleted).toList();

      // Cache frequently accessed notes for better performance
      _cacheActiveNotes();

      // Clean up expired trash items (older than 7 days)
      await _databaseService.deleteExpiredNotes();
      
      // Cancel all existing reminders and reschedule for active notes
      await _notificationService.cancelAllNoteReminders();
      
      // Schedule reminders for all active notes with reminders
      for (final note in [..._pinnedNotes, ..._notes, ..._archivedNotes]) {
        if (note.hasReminder && note.reminderTime != null && note.id != null) {
          await _notificationService.scheduleNoteReminder(note);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching notes: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Cache most recently accessed notes
  void _cacheActiveNotes() {
    // Cache most recent notes (up to 20)
    final recentNotes = [..._pinnedNotes, ..._notes]
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    
    for (var i = 0; i < recentNotes.length && i < 20; i++) {
      if (recentNotes[i].id != null) {
        _memoryService.cacheNote(recentNotes[i]);
      }
    }
  }

  Future<Note?> getNote(int id) async {
    // Try to get from cache first
    final cachedNote = _memoryService.getCachedNote(id);
    if (cachedNote != null) {
      return cachedNote;
    }
    
    // If not in cache, get from database
    final note = await _databaseService.getNote(id);
    
    // Cache the note for future access if found
    if (note != null) {
      _memoryService.cacheNote(note);
    }
    
    return note;
  }

  Future<int> addNote(Note note, {bool skipNotifyListeners = false}) async {
    _isLoading = true;
    if (!skipNotifyListeners) {
      notifyListeners();
    }

    try {
      final id = await _databaseService.insertNote(note);
      final newNote = note.copyWith(id: id);

      if (newNote.isArchived) {
        _archivedNotes.insert(0, newNote);
      } else if (newNote.isPinned) {
        _pinnedNotes.insert(0, newNote);
      } else {
        _notes.insert(0, newNote);
      }

      // Cache the new note
      _memoryService.cacheNote(newNote);

      // Schedule reminder notification if note has a reminder
      if (newNote.hasReminder && newNote.reminderTime != null) {
        await _notificationService.scheduleNoteReminder(newNote);
      }

      return id;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding note: $e');
      }
      return -1; // Return -1 to indicate error
    } finally {
      _isLoading = false;
      if (!skipNotifyListeners) {
        notifyListeners();
      }
    }
  }

  Future<void> updateNote(Note note, {bool skipNotifyListeners = false}) async {
    _isLoading = true;
    if (!skipNotifyListeners) {
      notifyListeners();
    }

    try {
      await _databaseService.updateNote(note);

      // Remove from all lists first
      _pinnedNotes.removeWhere((n) => n.id == note.id);
      _notes.removeWhere((n) => n.id == note.id);
      _archivedNotes.removeWhere((n) => n.id == note.id);
      _trashedNotes.removeWhere((n) => n.id == note.id);

      // Add to the appropriate list
      if (note.isArchived) {
        _archivedNotes.insert(0, note);
      } else if (note.isDeleted) {
        _trashedNotes.insert(0, note);
      } else if (note.isPinned) {
        _pinnedNotes.insert(0, note);
      } else {
        _notes.insert(0, note);
      }

      // Update the note in cache
      if (note.id != null) {
        if (note.isDeleted) {
          // Remove from cache if deleted
          _memoryService.removeCachedNote(note.id!);
        } else {
          // Update in cache
          _memoryService.cacheNote(note);
        }
      }

      // Handle reminder notification
      if (note.hasReminder && note.reminderTime != null && !note.isDeleted) {
        // Schedule or update reminder
        await _notificationService.scheduleNoteReminder(note);
      } else if (note.id != null) {
        // Cancel reminder if it was removed or note is deleted
        await _notificationService.cancelNoteReminder(note.id!);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating note: $e');
      }
    } finally {
      _isLoading = false;
      if (!skipNotifyListeners) {
        notifyListeners();
      }
    }
  }

  Future<void> moveToTrash(int id) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Find the note
      final note = [..._notes, ..._archivedNotes].firstWhere(
        (note) => note.id == id,
        orElse: () => throw Exception('Note not found'),
      );

      await _databaseService.moveToTrash(id);

      // Update local lists
      _notes.removeWhere((note) => note.id == id);
      _archivedNotes.removeWhere((note) => note.id == id);

      // Add to trashed notes
      final trashedNote = note.copyWith(
        isDeleted: true,
        deletedAt: DateTime.now(),
      );
      _trashedNotes.insert(0, trashedNote);
      
      // Remove from cache
      _memoryService.removeCachedNote(id);
      
      // Cancel any reminder for the note
      await _notificationService.cancelNoteReminder(id);
    } catch (e) {
      if (kDebugMode) {
        print('Error moving note to trash: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> deleteNotePermanently(int id) async {
    _isLoading = true;
    notifyListeners();

    try {
      await _databaseService.deleteNotePermanently(id);
      _trashedNotes.removeWhere((note) => note.id == id);
      
      // Remove from cache
      _memoryService.removeCachedNote(id);
      
      // Cancel any reminder for the note
      await _notificationService.cancelNoteReminder(id);
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting note permanently: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> archiveNote(int id) async {
    final note =
        [..._notes, ..._archivedNotes].firstWhere((note) => note.id == id);
    final updatedNote = note.copyWith(isArchived: true);
    await updateNote(updatedNote);
  }

  Future<void> unarchiveNote(int id) async {
    final note =
        [..._notes, ..._archivedNotes].firstWhere((note) => note.id == id);
    final updatedNote = note.copyWith(isArchived: false);
    await updateNote(updatedNote);
  }
  
  // Restore note from trash
  Future<void> restoreFromTrash(int id) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Find the note in trash
      final note = _trashedNotes.firstWhere(
        (note) => note.id == id,
        orElse: () => throw Exception('Note not found in trash'),
      );
      
      await _databaseService.restoreFromTrash(id);
      
      // Remove from trash list
      _trashedNotes.removeWhere((note) => note.id == id);
      
      // Add back to appropriate list
      final restoredNote = note.copyWith(
        isDeleted: false,
        deletedAt: null,
      );
      
      if (restoredNote.isArchived) {
        _archivedNotes.insert(0, restoredNote);
      } else {
        _notes.insert(0, restoredNote);
      }
      
      // Cache the restored note
      if (restoredNote.id != null) {
        _memoryService.cacheNote(restoredNote);
      }
      
      // Reschedule reminder if note has one
      if (restoredNote.hasReminder && restoredNote.reminderTime != null) {
        await _notificationService.scheduleNoteReminder(restoredNote);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error restoring note from trash: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Delete expired trash
  Future<void> _deleteExpiredTrash() async {
    try {
      // deleteExpiredNotes returns the count of deleted notes, not their IDs
      await _databaseService.deleteExpiredNotes();
      
      // Refresh the trash list from database to ensure it's up to date
      final allNotes = await _databaseService.getNotes();
      _trashedNotes = allNotes.where((note) => note.isDeleted).toList();
        
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting expired trash: $e');
      }
    }
  }
  
  // Empty the trash (delete all trashed notes permanently)
  Future<void> emptyTrash() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Store IDs before clearing for notification and cache cleanup
      final noteIds = _trashedNotes.map((note) => note.id!).toList();
      
      await _databaseService.emptyTrash();
      _trashedNotes.clear();
      
      // Clean up resources for all deleted notes
      for (final id in noteIds) {
        // Remove from cache
        _memoryService.removeCachedNote(id);
        // Cancel reminders
        await _notificationService.cancelNoteReminder(id);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error emptying trash: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Optimize memory usage
  void optimizeMemory() {
    _memoryService.cleanupCache();
  }

  // Toggle pin status of a note
  Future<void> toggleNotePin(int id) async {
    try {
      // Find the note in either pinned or unpinned lists
      Note? note = _pinnedNotes.firstWhere(
        (note) => note.id == id,
        orElse: () => _notes.firstWhere(
          (note) => note.id == id,
          orElse: () => throw Exception('Note not found'),
        ),
      );
      
      // Create updated note with toggled pin status
      final updatedNote = note.copyWith(isPinned: !note.isPinned);
      
      // Update in database
      await _databaseService.toggleNotePin(id, updatedNote.isPinned);
      
      // Update in memory
      if (updatedNote.isPinned) {
        _notes.removeWhere((note) => note.id == id);
        _pinnedNotes.insert(0, updatedNote);
      } else {
        _pinnedNotes.removeWhere((note) => note.id == id);
        _notes.insert(0, updatedNote);
      }
      
      // Update in cache
      _memoryService.cacheNote(updatedNote);
      
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error toggling note pin: $e');
      }
    }
  }

  // Update color for multiple notes
  Future<void> updateNotesColor(Set<int> noteIds, String colorValue) async {
    _isLoading = true;
    notifyListeners();

    try {
      for (final id in noteIds) {
        // Find the note
        Note? note;
        if (_pinnedNotes.any((n) => n.id == id)) {
          note = _pinnedNotes.firstWhere((n) => n.id == id);
        } else if (_notes.any((n) => n.id == id)) {
          note = _notes.firstWhere((n) => n.id == id);
        } else if (_archivedNotes.any((n) => n.id == id)) {
          note = _archivedNotes.firstWhere((n) => n.id == id);
        }

        if (note != null) {
          // Update the note with the new color
          final updatedNote = note.copyWith(themeColor: colorValue);
          await _databaseService.updateNote(updatedNote);

          // Update in memory lists
          if (note.isPinned) {
            final index = _pinnedNotes.indexWhere((n) => n.id == id);
            if (index != -1) {
              _pinnedNotes[index] = updatedNote;
            }
          } else if (note.isArchived) {
            final index = _archivedNotes.indexWhere((n) => n.id == id);
            if (index != -1) {
              _archivedNotes[index] = updatedNote;
            }
          } else {
            final index = _notes.indexWhere((n) => n.id == id);
            if (index != -1) {
              _notes[index] = updatedNote;
            }
          }

          // Update in cache
          _memoryService.cacheNote(updatedNote);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating notes color: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
