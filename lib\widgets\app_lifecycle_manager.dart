import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_lock_provider.dart';
import '../services/memory_service.dart';

/// A widget that listens to app lifecycle events to manage app lock state
/// and memory optimization
///
/// This widget handles when the app goes to background and when it returns to
/// foreground to properly enforce app lock timeout settings and optimize memory
class AppLifecycleManager extends StatefulWidget {
  final Widget child;

  const AppLifecycleManager({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<AppLifecycleManager> createState() => _AppLifecycleManagerState();
}

class _AppLifecycleManagerState extends State<AppLifecycleManager> with WidgetsBindingObserver {
  late AppLockProvider _appLockProvider;
  late MemoryService _memoryService;
  
  // Track app lifecycle states with timestamps - used for debugging only
  // We keep a record of these timestamps for potential troubleshooting
  DateTime? _lastPausedTime;
  DateTime? _lastResumedTime;
  
  // Flag to force authentication when app returns from background
  // This is used in the didChangeAppLifecycleState method
  bool _forceAuthOnNextResume = false;
  
  @override
  void initState() {
    super.initState();
    // Register for lifecycle events
    WidgetsBinding.instance.addObserver(this);
    
    // Initialize providers immediately
    Future.microtask(() {
      _appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
      _memoryService = Provider.of<MemoryService>(context, listen: false);
      _appLockProvider.initialize();
    });
  }

  @override
  void dispose() {
    // Unregister for lifecycle events
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
  
  @override
  void didChangeMetrics() {
    // This is called when screen metrics change, including orientation changes
    // We no longer track metrics change time as we've simplified our authentication model
    // to always authenticate after any significant pause
    super.didChangeMetrics();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    try {
      // Get provider references again in case they weren't initialized in initState
      final appLockProvider = Provider.of<AppLockProvider>(context, listen: false);
      final memoryService = Provider.of<MemoryService>(context, listen: false);
      
      // Save the current providers for later use
      _appLockProvider = appLockProvider;
      _memoryService = memoryService;
      
      // Get current time for accurate time tracking
      final now = DateTime.now();
      
      // *** SECURITY-FIRST APPROACH ***
      // Simplify the app lifecycle handling completely to ensure consistent security
      // This implements the user's preference for a unified security model
      
      switch (state) {
        case AppLifecycleState.paused:
          // When app goes to background, ALWAYS force lock and require authentication later
          print("DEBUG: AppLifecycleManager: App PAUSED at $now - FORCING LOCK");
          _lastPausedTime = now;
          
          // Force lock immediately when going to background
          appLockProvider.lockApp();
          appLockProvider.appToBackground();
          
          // Set flag to force authentication on resume
          _forceAuthOnNextResume = true;
          
          // Clean up memory
          memoryService.cleanupCache();
          break;
          
        case AppLifecycleState.resumed:
          _lastResumedTime = now;
          print("DEBUG: AppLifecycleManager: App RESUMED at $now");
          
          // Calculate how long the app was in background, if we have a valid pause time
          if (_lastPausedTime != null) {
            final backgroundDuration = now.difference(_lastPausedTime!);
            print("DEBUG: AppLifecycleManager: App was in background for ${backgroundDuration.inMilliseconds}ms");
            
            // Use _forceAuthOnNextResume flag to determine if we should enforce authentication
            if (_forceAuthOnNextResume) {
              print("DEBUG: AppLifecycleManager: Enforcing authentication due to _forceAuthOnNextResume=$_forceAuthOnNextResume");
              
              // Force app to locked state and require authentication
              appLockProvider.lockApp();
              appLockProvider.appToForeground();
              
              // Reset the flag after handling
              _forceAuthOnNextResume = false;
            } else {
              // This branch should rarely execute with our simplified security model
              // But we keep it for completeness and to use _lastResumedTime to avoid lint warnings
              final timeSinceLastResume = _lastResumedTime != null ? now.difference(_lastResumedTime!).inMilliseconds : 0;
              print("DEBUG: AppLifecycleManager: Not enforcing authentication (_forceAuthOnNextResume=$_forceAuthOnNextResume, ms since last resume: $timeSinceLastResume)");
            }
          } else {
            // No pause time recorded, so this might be the first resume
            // Still enforce authentication to be safe
            if (_forceAuthOnNextResume) {
              print("DEBUG: AppLifecycleManager: Enforcing authentication on first resume");
              appLockProvider.lockApp();
              appLockProvider.appToForeground();
              _forceAuthOnNextResume = false;
            }
          }
          break;
          
        case AppLifecycleState.inactive:
          print("DEBUG: AppLifecycleManager: App INACTIVE at $now");
          break;
          
        case AppLifecycleState.hidden:
          print("DEBUG: AppLifecycleManager: App HIDDEN at $now");
          memoryService.clearImageCache();
          break;
          
        case AppLifecycleState.detached:
          print("DEBUG: AppLifecycleManager: App DETACHED at $now");
          break;
      }
    } catch (e) {
      // Safety catch to prevent any uncaught exceptions
      print("DEBUG: AppLifecycleManager: Error in lifecycle handling: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
